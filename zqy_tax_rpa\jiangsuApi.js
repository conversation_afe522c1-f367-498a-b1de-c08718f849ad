const Untils = require('./untils')
const request = require('sync-request'); //默认同步请求
const pbottleRPA = require('./pbottleRPA')
const xlsx = require("node-xlsx");
const fs = require('fs')
const fss = require('fs').promises
const php = require("php2javascript");
const {
	FormData
} = require('sync-request');
const ExcelJS = require('exceljs');
const path = require('path');
const compressing = require('compressing');
const workSheetsFromFile = xlsx.parse(`${__dirname}\\配置项.xlsx`);
const config_sheet = workSheetsFromFile[0].data;
const global_download_path = config_sheet[1][2];
class ExtendClass {
	constructor(proItem, pbottleRPA, childrenTask) {

		this.proItem = proItem; // 将传入的值存储在类的属性中
		this.childrenTask = childrenTask; // 将传入的值存储在类的属性中
		this.pbottleRPA = pbottleRPA
		this.untils = new Untils()

	}

	rpa_to_login() {
		this.pbottleRPA.keyTap('Ctrl + Shift + W');
		let url =
			`https://tpass.jiangsu.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.jiangsu.chinatax.gov.cn%3A8443%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=mcsc7e2ssscb4sfmbsmas35sass2753b&response_type=code&state=aea49396febf447fbc6164d6ad5f2dc4&client_pid=mcsc7e2ssscb4sfmbsmas35sass2753b`
		this.pbottleRPA.openURL(url)
		this.pbottleRPA.sleep(500)
		this.untils.closeOtherPages()
		this.pbottleRPA.keyTap('alt+space')
		this.pbottleRPA.keyTap('x') // 浏览器最大化
		let ready = `input[placeholder="统一社会信用代码/纳税人识别号"]`
		if (ready) {
			console.log('完成RPA打开税局页面节点')
		} else {
			this.untils.terminateTask(
				this.childrenTask.flowId,
				3,
				this.childrenTask.variable,
				'进入登录页失败'
			)
			console.log('进入登录页失败')
            this.pbottleRPA.exit('进入登录页失败')

		}
		return 1


		// this.pbottleRPA.keyTap('Ctrl + Shift + W');
		// let url = `https://etax.${this.proItem.url}.chinatax.gov.cn/sso/login`
		// let res1 = request('GET', url);
		// var regex = /var kxUrl = "([^"]+)"/;
		// const matchResult = res1.body.toString().match(regex); // 将响应体转换为字符串
		// if (matchResult && matchResult.length > 1) {
		// 	const kxUrl = matchResult[1];
		// 	console.log(kxUrl); // 输出提取到的 kxUrl
		// 	this.pbottleRPA.openURL(kxUrl)
		// 	this.pbottleRPA.keyTap('alt+space')
		// 	this.pbottleRPA.keyTap('x') // 浏览器最大化
		// } else {
		// 	console.log('未找到 kxUrl');
		// }

		// let ready = `input[placeholder="统一社会信用代码/纳税人识别号"]`
		// if (ready) {
		// 	console.log('完成RPA打开税局页面节点')
		// } else {
		// 	this.untils.terminateTask(
		// 		this.childrenTask.flowId,
		// 		3,
		// 		this.childrenTask.variable,
		// 		'进入登录页失败'
		// 	)
		// 	console.log('进入登录页失败')
        //     this.pbottleRPA.exit('进入登录页失败')

		// }
		// return
	}


	// 选择登录身份
	select_sf() {
		// value=03：办税员   value=05 管理员  
		let select = this.untils.isImage("/input/1920/sflx.png", 5)
		if (select) {
			this.pbottleRPA.browserCMD_click(`div[aria-label="身份类型选择"] button span`)
			this.pbottleRPA.sleep(3000)
			let res = this.untils.completeTask(
				this.childrenTask.id, 
				this.childrenTask.flowId, 
				this.childrenTask.flowKey, 
				this.childrenTask.taskKey,
				this.childrenTask.variable, 
				1, 
				"选择身份成功"
			)
		} else {
			this.pbottleRPA.browserCMD_click(`div[aria-label="身份类型选择"] button span`)
			this.pbottleRPA.sleep(3000)
			console.log('没找到该图片2')
			let res = this.untils.completeTask(
				this.childrenTask.id, 
				this.childrenTask.flowId, 
				this.childrenTask.flowKey, 
				this.childrenTask.taskKey, 
				this.childrenTask.variable, 
				1, 
				"无需选择身份"
			)
		}
		return 1
	}



	formatDate(date) {
		var year = date.getFullYear();
		var month = this.padZero(date.getMonth() + 1);
		var day = this.padZero(date.getDate());
		return year + '-' + month + '-' + day;
	}

	// 给小于10的数字前面补0
	padZero(num) {
		return num < 10 ? '0' + num : '' + num;
	}


}

module.exports = ExtendClass;
