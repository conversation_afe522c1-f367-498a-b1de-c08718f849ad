const pbottleRPA = require('./pbottleRPA')
const xlsx = require("node-xlsx");
const fs = require('fs')
const path = require('path');
const php = require("php2javascript");
const request = require('sync-request'); //默认同步请求
const {
	FormData
} = require('sync-request');
const compressing = require('compressing');
const axios = require("axios");
const Untils = require("./untils")


// ZX();
Test2()




function Test2() {
	let cookie = '';
	pbottleRPA.sleep('2000');
	let res = pbottleRPA.browserCMD_cookie(Cookie,'','')
	console.log(res)

}

 function Test1(){
	let cookie = '';
	// pbottleRPA.openURL(`https://etax.jiangsu.chinatax.gov.cn:8443/szzh/?ruuid=1739516101445`)
	pbottleRPA.moveAndClick(1300,700)
	let error =  pbottleRPA.browserCMD_text(`div[class="t-message t-is-error"]`)
	 console.log('error',error)
	// pbottleRPA.sleep(5000)
	// let CSQYMC =pbottleRPA.browserCMD_text(`div[class="title"]`)
	// pbottleRPA.sleep(1000)
	// console.log(CSQYMC)
	// if(CSQYMC !== 'ok'){
	// 	pbottleRPA.keyTap('Ctrl + Shift + J')
	// 	pbottleRPA.paste(`允许粘贴`)
	// 	pbottleRPA.sleep(1000)
	// 	pbottleRPA.keyTap('ENTER')
	// 	pbottleRPA.sleep(1000)
	// 	pbottleRPA.paste(`let result = document.cookie; console.log(result); copy(result)`)
	// 	pbottleRPA.keyTap('ENTER')
	// 	console.log(pbottleRPA.getClipboard())
	// 	cookie = pbottleRPA.getClipboard()
	// 	console.log('cookie',cookie)
	// 	pbottleRPA.keyTap('ctrl+W')
	// } else{
	// 	console.log('cookie',cookie)
	// 	console.log("Cookie获取失败！")
	// 	pbottleRPA.keyTap('ctrl+W')
	// }
}


function ZX() {
	pbottleRPA.sleep(5000)
	pbottleRPA.browserCMD_click(
		`div.com-sub-content > div > div > div > div > div.upper > div.right > button > span > span`
	)
	let xiazaichenggong = existImage("/input/1920/xiazaichenggong.png")
	if(xiazaichenggong === true)
	{
		pbottleRPA.sleep(1000)
		pbottleRPA.keyTap('ALT+LEFT')
	}else{	
		pbottleRPA.sleep(1000)
		pbottleRPA.keyTap('ALT+LEFT')
	}
}

function existImage(tpPath, intervalFun = () => {}, timeOut = 15) {
		for (let index = 0; index < timeOut; index++) {
			pbottleRPA.sleep(500)
			let position = pbottleRPA.findScreen(tpPath);
			if (position !== false) {
				return true; // 找到图片，返回true
			}

			if (intervalFun() == 'stopWait') {
				console.log('stopWait from intervalFun');
				return false;
			}
		}
		// 图片未找到，返回false
		return false;
	}
	