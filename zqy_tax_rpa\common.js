const Untils = require('./untils')
const request = require('sync-request'); //默认同步请求

const pbottleRPA = require('./pbottleRPA')
const xlsx = require("node-xlsx");
const fs = require('fs')
const php = require("php2javascript");
const {
	FormData
} = require('sync-request');

const {readyToRPAToLogin, getCookie} = require('./bridge2.js');
const workSheetsFromFile = xlsx.parse(`${__dirname}\\配置项.xlsx`);
const config_sheet = workSheetsFromFile[0].data;
const global_download_path = config_sheet[1][2];
const compressing = require('compressing');
const { ok } = require('assert');
class ExtendClass {
	constructor(proItem, pbottleRPA, childrenTask) {
		// proItem 省份信息
		//pbottleRPA 机器人实例
		//childrenTask子任务信息
		this.proItem = proItem; // 将传入的值存储在类的属性中
		this.childrenTask = childrenTask; // 将传入的值存储在类的属性中
		this.pbottleRPA = pbottleRPA
		this.untils = new Untils()

		this.newtaskId = '’'
	}

	async rpa_login() {
		// rpa_to_login
		console.log('进入rpa_to_login')
		await readyToRPAToLogin(this.proItem, this.pbottleRPA, this.childrenTask)
		this.pbottleRPA.sleep(1000)

		console.log('进入rpa_login')
		console.log('JSON.parse(this.childrenTask.variable)',JSON.parse(this.childrenTask.variable))
		let ready = this.untils.waitImage("/input/1920/loginready.png")
		this.pbottleRPA.moveMouseSmooth(ready.x, ready.y)
		this.pbottleRPA.mouseClick() //focus
		this.pbottleRPA.keyTap('ctrl+a')
		this.pbottleRPA.paste(JSON.parse(this.childrenTask.variable).nsrsbh)
		this.pbottleRPA.keyTap('tab')
		this.pbottleRPA.keyTap('ctrl+a')
		this.pbottleRPA.paste(JSON.parse(this.childrenTask.variable).account)
		this.pbottleRPA.keyTap('tab')
		this.pbottleRPA.keyTap('ctrl+a')
		this.pbottleRPA.paste(JSON.parse(this.childrenTask.variable).password)
		this.pbottleRPA.keyTap('tab')

		this.pbottleRPA.moveMouse(ready.x - 153, ready.y + 180)
		this.pbottleRPA.mouseLeftDragTo(ready.x - 153 + 370, ready.y + 180)
		this.pbottleRPA.browserCMD_click(`button span:contains(登录)`)

		try {
			let value = await this.untils.checkLogin()
				//根据flowkey判断是否要在rpa_login完成该节点
				pbottleRPA.sleep(1000)
				if(this.childrenTask.flowKey == 'no_code'){

					// 在此节点检查登录身份,检查身份后直接完成
					console.log('进入select_sf')
					let select_sfRes = await this.select_sf()
					console.log('select_sf' , select_sfRes)

					console.log('完成RPA登录税局节点111')
					this.untils.addLog(global.traceId, '完成RPA登录税局节点', 'rpa_login')
				}else{
					console.log('完成RPA登录税局节点222')
					this.untils.addLog(global.traceId, '完成RPA登录税局节点', 'rpa_login')
				}

		} catch (e) {
			console.log("检查状态:", e)
			// 添加错误日志
			// 账号信息有误，回滚
			let rollBackRes = this.untils.completeTask(
				this.childrenTask.id,
				this.childrenTask.flowId,
				this.childrenTask.flowKey,
				this.childrenTask.taskKey,
				this.childrenTask.variable,
				0,
				`${e.message}`
			)
			console.log('rollBackRes', rollBackRes)
			return 0
		}
		return 1
	}

	//登录并获取短信验证码 
	 async get_code() {

		console.log('开始进行登录操作')
		let rpa_loginRes = await this.rpa_login();
		if (rpa_loginRes == 0) {
			return
		}
		this.pbottleRPA.sleep(1000)

		this.pbottleRPA.browserCMD_click(`div span:contains(短信验证)`)
		this.pbottleRPA.browserCMD_click(`button span:contains(获取验证码)`)
		let codeerr = this.pbottleRPA.browserCMD_text(
			`body > div.el-message.el-message--warning.el-icon-warning-color > p`)
		console.log('codeerr', codeerr)
		if (codeerr.includes("过于频繁")) {
			//验证失败 过于频发发送验证码，当前链接任务无效，需重新启动任务
			let rollBackRes = this.untils.completeTask(
				this.childrenTask.id,
				this.childrenTask.flowId,
				this.childrenTask.flowKey,
				this.childrenTask.taskKey,
				this.childrenTask.variable,
				0,
				`${codeerr}`
			)
			console.log('rollBackRes', rollBackRes)
			return
		}

		//完成用户输入账密节点
		let res = this.untils.completeTask(
			this.childrenTask.id, 
			this.childrenTask.flowId, 
			this.childrenTask.flowKey, 
			this.childrenTask.taskKey, 
			this.childrenTask.variable, 
			1, 
			"获取验证码成功"
		)
		console.log('完成RPA获取验证码节点', res)
		return
	}


	//校验验证码是否输入正确
	async check_code() {
		for (let index = 0; index <= 300; index++) { //等300秒
			//获取填写状
			this.pbottleRPA.sleep(1000);
			let VeriCode = JSON.parse(this.childrenTask.variable).smsCode
			console.log("用户填写的验证码", VeriCode)
			if (VeriCode) {
				// pbottleRPA.browserCMD_click(`input[placeholder="请输入短信验证码"] `) 720 428
				let yanzhengma = `input[placeholder="请输入短信验证码"]`
				if (yanzhengma) {
					this.pbottleRPA.browserCMD_click(`input[placeholder="请输入短信验证码"] `)
				}
				this.pbottleRPA.keyTap('ctrl+a')
				this.pbottleRPA.paste(VeriCode)
				this.pbottleRPA.browserCMD_click(`button span:contains(登录)`)

				//验证码判断body > div.el-message.el-message--warning.el-icon-warning-color > p
				// let codeerr = this.pbottleRPA.findScreen("/input/1920/codeerr.png")
				// console.log('codeerr', codeerr)
				let codeerr = this.pbottleRPA.browserCMD_text(
					`body > div.el-message.el-message--warning.el-icon-warning-color > p`)
				console.log('codeerr', codeerr)
				if ( codeerr.includes("错误") || codeerr.includes("失效") || codeerr != 'ok') {
					//验证失败
					let rollBackRes = this.untils.completeTask(
						this.childrenTask.id,
						this.childrenTask.flowId,
						this.childrenTask.flowKey,
						this.childrenTask.taskKey,
						this.childrenTask.variable,
						0,
						`${codeerr}`
					)
					console.log('rollBackRes', rollBackRes)
					return
				} else {
					console.log('验证码正确，登录成功')
				}
				break;
			}
			console.log('进入下一步')
			if (index == 300) {
				let res = this.untils.terminateTask(
					this.childrenTask.flowId,
					3,
					this.childrenTask.variable,
					'验证码未输入，终止任务'
				)
				this.pbottleRPA.exit('验证码未输入，终止任务')
			}
		}

		// 检查登录身份
		console.log('进入select_sf')
		let select_sfRes = await this.select_sf()
		console.log('select_sf' , select_sfRes)
		
		return {}
	}

	// 保活-获取登录二维码
	async commit_qr_code(){
		// rpa_to_login
		console.log('开始进行登录操作')
		let rpa_to_login111 = await readyToRPAToLogin(this.proItem, this.pbottleRPA, this.childrenTask)
		console.log('rpa_to_login' , rpa_to_login111)
		this.pbottleRPA.sleep(2000)

		for (let index = 0; index <= 10; index++) {
			let imagecode = this.pbottleRPA.browserCMD_attr('div#qrcodeDiv img', 'src')
			// let imagecode = this.pbottleRPA.browserCMD_attr('div#qrcodeDiv img', 'data-src')
			// let imagecode = imagecode1111.split(',')[1];
			console.log('二维码图片：', imagecode.substring(0, 50));

			if (imagecode.length > 50) {

				console.log('imagecode', imagecode) 
				let	res = this.untils.httpfpOnceAuth('POST','onceAuth/onceAuth/scanQrcode',{
					'account': JSON.parse(this.childrenTask.variable).account,
					'qrCode': imagecode,
					'nsrsbh': JSON.parse(this.childrenTask.variable).nsrsbh,
				}, null, false)
				this.untils.addLog(global.traceId, `${JSON.stringify(res)}`, 'commit_qr_code')
				console.log('res', res)

				if(res.code == '0'){		
					console.log('获取二维码成功')
					// 新增登录成功的校验
					this.checkCodeLogin()
				}	
				else {
					const res = this.untils.terminateTask(
						this.childrenTask.flowId,
						2,
						this.childrenTask.variable,
						`${res.msg}`
					)
					pbottleRPA.exit('登录失败:' + this.childrenTask.name)
				}
				break;
			}
			if (index == 10) {
				const res = this.untils.terminateTask(
					this.childrenTask.flowId,
					3,
					this.childrenTask.variable,
					'未获取到二维码图片'
				)
				this.pbottleRPA.exit('未获取到二维码图片');
			}
		}
		return;
	}

	// 获取登录二维码
	async codelogin() {

		// rpa_to_login
		console.log('开始进行登录操作')
		let rpa_to_login111 = await readyToRPAToLogin(this.proItem, this.pbottleRPA, this.childrenTask)
		console.log('rpa_to_login' , rpa_to_login111)
		this.pbottleRPA.sleep(2000)

		//二维码登录
		for (let index = 0; index <= 10; index++) {
			let imagecode = this.pbottleRPA.browserCMD_attr('div#qrcodeDiv img', 'src')
			// let imagecode = imagecode1111.split(',')[1];
			console.log('二维码图片：', imagecode.substring(0, 50));
			console.log('global.traceId',global.traceId)
			if (imagecode.length > 50) {
				// 完成子任务  //发送二维码
				
				let rs = this.untils.completeTask(
					this.childrenTask.id,
					this.childrenTask.flowId,
					this.childrenTask.flowKey,
					this.childrenTask.taskKey,
					JSON.stringify({qrCode: imagecode}),
					1,
					`获取二维码成功`
				)
				
				if (rs.state !== 'success') {
					this.untils.terminateTask(
						this.childrenTask.flowId,
						2,
						this.childrenTask.variable,
						'发送二维码子任务提交失败'
					)
					pbottleRPA.exit('发送二维码子任务提交失败:' + this.childrenTask.name)
				}
				break;
			}
			if (index == 10) {
				this.untils.terminateTask(
					this.childrenTask.flowId,
					2,
					this.childrenTask.variable,
					'未获取到二维码图片'
				)
				this.pbottleRPA.exit('未获取到二维码图片');
			}
		}
		return;
	}

	checkCodeLogin() {
		let startTime = Date.now();
		let endTime = startTime + 300000; // 设置结束时间为当前时间加上300秒（300000毫秒）

		while (Date.now() < endTime) {
			// 老猫本地
			// let rs = this.untils.http('GET', `operate/getFlowStatus`, null,{
			// 	'flowId':this.childrenTask.flowId,
			// }, true)
			// console.log("rs", rs)

			let rs = this.untils.http('GET', `flow/operate/getFlowStatus`, null,{
				'flowId':this.childrenTask.flowId,
			}, true)
			console.log("rs", rs)


			if (rs.data.status !== 1) {
				const res = this.untils.terminateTask(
					this.childrenTask.flowId,
					3,
					this.childrenTask.variable,
					'任务前端终止'
				)
				this.pbottleRPA.exit('任务前端终止');
			}

			let imagecode = this.pbottleRPA.browserCMD_attr('div#qrcodeDiv img', 'src');
			if (imagecode !== 'ok') {
				console.log('未扫码，剩余时间：', Math.ceil((endTime - Date.now()) / 1000), '秒');
				new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
			} else {
				let res = this.untils.completeTask(
					this.childrenTask.id, 
					this.childrenTask.flowId, 
					this.childrenTask.flowKey, 
					this.childrenTask.taskKey,
					this.childrenTask.variable, 
					1, 
					"扫码成功，正在验证中···"
				);
				console.log('完成结果', res);
				return {}; // 返回空对象表示任务完成
			}
		}

		let res = this.untils.terminateTask(
			this.childrenTask.flowId,
			2,
			this.childrenTask.variable,
			'用户未扫码，终止任务'
		)
		this.pbottleRPA.exit('用户未扫码，终止任务');

		return {}; // 如果循环结束仍未完成任务，返回空对象
	}


	commit_login_result() {
		//判断有没有登录成功，这里默认成功，直接完成，实际上应判断页面元素，如每省份不同，因单独写
		let qymc1 = JSON.parse(this.childrenTask.variable).qymc
		let qymc2 = this.pbottleRPA.browserCMD_text(
			`div[class="leftTopItem clearfix"] div:nth-child(1)`)
		
		console.log('当前登录企业名称-', qymc1, qymc2)
		if (qymc1 != qymc2.trim()) {

			let res = this.untils.terminateTask(
				this.childrenTask.flowId,
				3,
				this.childrenTask.variable,
				'授权失败,税号公司名称不匹配，请检查！请打开电子税务局APP，在首页顶部找到并点击【身份切换】功能，选择相应的企业身份后，重新执行您的操作。'
			)
			console.log('res', res)
			
			this.pbottleRPA.keyTap('Ctrl + Shift + W');
			this.pbottleRPA.exit('授权失败,税号公司名称不匹配，终止任务');

		} else {
			let res = this.untils.completeTask(
				this.childrenTask.id, 
				this.childrenTask.flowId,
				this.childrenTask.flowKey,
				this.childrenTask.taskKey, 
				this.childrenTask.variable, 
				 1, 
				 "登录成功"
				)
			console.log('完成结果', res)
		}

		return {}
	}

	async gather_invoice() {

		this.pbottleRPA.sleep(5000)
		console.log("=====开始获取财税Cookie=======")
		let cookie = this.getcookie(this.proItem)
		console.log('Cookie获取完成',cookie)
		this.pbottleRPA.sleep(2000)

		this.pbottleRPA.openURL(
			`https://tpass.${this.proItem.url}.chinatax.gov.cn:8443/#/userCenterEP/baseInfoEP`)
		let geren = this.untils.waitImage("/input/1920/geren.png")
		if (geren) {
			this.pbottleRPA.browserCMD_click(`div[class="enterprise-name"] img[title="显示完整"]`)
		}

		var myUsername = this.pbottleRPA.browserCMD_text(`div[class="enterprise-name"] span`)
		console.log(",,,,,,,,,,", myUsername)


		console.log('进入gather_invoice')
		this.pbottleRPA.openURL(
			`https://dppt.${this.proItem.url}.chinatax.gov.cn:8443/invoice-query/invoice-query/`)

		var count = 0
		let chaxuntype = this.untils.waitImage("/input/1920/chaxuntype.png")
		if (chaxuntype) {
			this.pbottleRPA.moveMouseSmooth(chaxuntype.x, chaxuntype.y)
		}
		//广东省循环逐月采集 ，其他省份逐年采集
		if(this.proItem.url === 'guangdong' ){
			console.log(`开始逐月采集，当前省份为${this.proItem.url}`)
			this.untils.addLog(global.traceId, `开始逐月采集，当前省份为${this.proItem.url}`, 'gather_invoice')
			for (let index = 0; index < 2; index++) {
				if (index == 1) {
					//chaxuntype.png
					this.pbottleRPA.moveMouseSmooth(chaxuntype.x + 150, chaxuntype.y)
					this.pbottleRPA.mouseClick() //focus
					this.pbottleRPA.browserCMD_click(`li span:contains(取得发票)`)
				}

				const fplx = (index === 0) ? '销项' : '进项';
				// 获取当前日期
				var currentDate = new Date();

				// 获取当前日期的上一个月
				currentDate.setMonth(currentDate.getMonth() - 1);
				// 循环输出36个月的数据
				for (var i = 1; i <= 36; i++) {
					let currentCount = 0;
					if (index == 1 && count != currentCount) {
						//chaxuntype.png
						currentCount = count
						this.pbottleRPA.moveMouseSmooth(chaxuntype.x + 150, chaxuntype.y)
						this.pbottleRPA.mouseClick() //focus
						this.pbottleRPA.browserCMD_click(`li span:contains(取得发票)`)
					}
					// 获取11个月前的日期
					var startDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);

					// 获取当前月份的最后一天
					var endDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);

					// 输出日期范围
					console.log(formatDate(startDate) + '---------->' + formatDate(endDate));

					// 将当前日期往前推1个月
					currentDate.setMonth(currentDate.getMonth() - 1);

					this.pbottleRPA.browserCMD_click(`input[placeholder="开票日期起"]`)
					this.pbottleRPA.browserCMD_val('input[placeholder="开票日期起"]', formatDate(startDate));
					this.pbottleRPA.keyTap('Enter')

					this.pbottleRPA.browserCMD_click(`input[placeholder="开票日期止"]`)
					this.pbottleRPA.browserCMD_val('input[placeholder="开票日期止"]', formatDate(endDate));
					this.pbottleRPA.keyTap('Enter')

					this.pbottleRPA.browserCMD_click(`button span:contains(查询)`)
					console.log("-------------查询结束---------------")
					this.pbottleRPA.sleep(1000)
					let daochu = this.pbottleRPA.browserCMD_text('div[class="button__export"] > button > span:nth-child(1)')
					if (daochu === 'ok') {
						console.log("-------------查询无发票数据--------------")
						this.untils.addLog(global.traceId, `${formatDate(startDate)}` + '---' + `${formatDate(endDate)}` + "年度无" +`${fplx}` +"数据", 'gather_invoice')
						console.log(`${formatDate(startDate)}` + '---' + `${formatDate(endDate)}` + "年度无" +
							`${fplx}` +
							"数据")
					}else{
						this.pbottleRPA.sleep(500)
						let text = this.pbottleRPA.browserCMD_text(
							`div[class="t-pagination__total"]`) //div[class = "statistics-info"] span:nth-child(1)
						let match = text.match(/\d+/);
						let number = match ? parseInt(match[0]) : 0;
						console.log("===============", text, number)
						this.pbottleRPA.browserCMD_click(`button span:contains(导出 )`)
						this.pbottleRPA.browserCMD_click(`li span:contains(导出全部)`)
						let downloading = this.untils.existImage("/input/1920/dowloading.png")
						// let wxts = this.untils.waitImage("/input/1920/wxts.png")
						if (downloading) {
							console.log("-------------直接开始下载---------------")
								break; 
						} else if (!downloading) {
							let submissionTimes = [];
							count++;
							// 发票数量大于2500，异步下载
							console.log("-------------提交异步下载---------------")
							if (!downloading) {
								console.log("温馨提示")
								let wxts1 = this.untils.waitImage("/input/1920/wxts.png")
								this.pbottleRPA.sleep(500)
								this.pbottleRPA.moveMouseSmooth(wxts1.x + 20, wxts1.y + 180)
								this.pbottleRPA.mouseClick() //focus
							}
							this.untils.addLog(global.traceId, `${formatDate(startDate)}` + '---' + `${formatDate(endDate)}` +`${fplx}` +"数据提交异步下载", 'gather_invoice')
							this.pbottleRPA.sleep('30000');
							this.pbottleRPA.keyTap('alt + Left')

						} 
						let down1 = this.untils.waitImageDisappear("/input/1920/dowloading.png")
						if (down1) {
							console.log("-------------已下载---------------")
							this.untils.addLog(global.traceId, `${formatDate(startDate)}` + '---' + `${formatDate(endDate)}` + '数据下载完成', 'gather_invoice')
							this.pbottleRPA.sleep(2000)
						}
					}

				}
			}
		}else {
			for (let index = 0; index < 2; index++) {
				console.log(`开始逐年采集，当前省份为${this.proItem.url}`)
				this.untils.addLog(global.traceId, `开始逐年采集，当前省份为${this.proItem.url}`, 'gather_invoice')
				if (index == 1) {
					//chaxuntype.png
					this.pbottleRPA.moveMouseSmooth(chaxuntype.x + 150, chaxuntype.y)
					this.pbottleRPA.mouseClick() //focus
					this.pbottleRPA.browserCMD_click(`li span:contains(取得发票)`)
				}

				const fplx = (index === 0) ? '销项' : '进项';
				// 获取当前日期
				var currentDate = new Date();

				// 获取当前日期的上一个月
				currentDate.setMonth(currentDate.getMonth() - 1);
				// 循环输出3年的数据
				for (var i = 1; i <= 3; i++) {
					let currentCount = 0;
					if (index == 1 && count != currentCount) {
						//chaxuntype.png
						currentCount = count
						this.pbottleRPA.moveMouseSmooth(chaxuntype.x + 150, chaxuntype.y)
						this.pbottleRPA.mouseClick() //focus
						this.pbottleRPA.browserCMD_click(`li span:contains(取得发票)`)
					}
					// 获取11个月前的日期
					var startDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - 11, 1);

					// 获取当前月份的最后一天
					var endDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);

					// 输出日期范围
					console.log(formatDate(startDate) + '---------->' + formatDate(endDate));

					// 将当前日期往前推12个月
					currentDate.setMonth(currentDate.getMonth() - 12);

					this.pbottleRPA.browserCMD_click(`input[placeholder="开票日期起"]`)
					this.pbottleRPA.browserCMD_val('input[placeholder="开票日期起"]', formatDate(startDate));
					this.pbottleRPA.keyTap('Enter')

					this.pbottleRPA.browserCMD_click(`input[placeholder="开票日期止"]`)
					this.pbottleRPA.browserCMD_val('input[placeholder="开票日期止"]', formatDate(endDate));
					this.pbottleRPA.keyTap('Enter')

					this.pbottleRPA.browserCMD_click(`button span:contains(查询)`)
					console.log("-------------查询结束---------------")
					this.pbottleRPA.sleep(1000)
					let daochu = this.pbottleRPA.browserCMD_text('div[class="button__export"] > button > span:nth-child(1)')
					if(daochu === 'ok') {
							console.log("-------------查询无发票数据--------------")
							console.log(`${formatDate(startDate)}` + '---' + `${formatDate(endDate)}` + "年度无" +
								`${fplx}` +
								"数据")
							this.untils.addLog(global.traceId, `${formatDate(startDate)}` + '---' + `${formatDate(endDate)}` + "年度无" +`${fplx}` +"数据", 'gather_invoice')
						}
					else{
						this.pbottleRPA.sleep(500)
						let text = this.pbottleRPA.browserCMD_text(
							`div[class="t-pagination__total"]`) //div[class = "statistics-info"] span:nth-child(1)
						let match = text.match(/\d+/);
						let number = match ? parseInt(match[0]) : 0;
						console.log("===============", text, number)
						this.pbottleRPA.browserCMD_click(`button span:contains(导出 )`)
						this.pbottleRPA.browserCMD_click(`li span:contains(导出全部)`)
						let downloading = this.untils.existImage("/input/1920/dowloading.png")
						// let wxts = this.untils.existImage("/input/1920/wxts.png")
						// let wxts = this.untils.waitImage("/input/1920/wxts.png")
						if (downloading) {
							console.log("-------------直接开始下载---------------")
								break; 
						} else if (!downloading) {
							let submissionTimes = [];
							count++;
							// 发票数量大于2500，异步下载
							console.log("-------------提交异步下载---------------")
							if (!downloading) {
								console.log("温馨提示")
								let wxts1 = this.untils.waitImage("/input/1920/wxts.png")
								this.pbottleRPA.sleep(500)
								this.pbottleRPA.moveMouseSmooth(wxts1.x + 20, wxts1.y + 180)
								this.pbottleRPA.mouseClick() //focus
							}
							this.untils.addLog(global.traceId, `${formatDate(startDate)}` + '---' + `${formatDate(endDate)}` +`${fplx}` +"数据提交异步下载", 'gather_invoice')
							this.pbottleRPA.sleep('30000');
							this.pbottleRPA.keyTap('alt + Left')

							break;
						} 
						let down1 = this.untils.waitImageDisappear("/input/1920/dowloading.png")
						if (down1) {
							console.log("-------------已下载---------------")
							this.untils.addLog(global.traceId, `${formatDate(startDate)}` + '---' + `${formatDate(endDate)}` + '数据下载完成', 'gather_invoice')
							this.pbottleRPA.sleep(2000)
						}
					}

				}
			}
		}
		console.log("count+++++++++++++++++++++++++++++", count)

		if (count > 0) {
			this.pbottleRPA.openURL(
				`https://dppt.${this.proItem.url}.chinatax.gov.cn:8443/importing-exporting-batches-search`)
			this.pbottleRPA.keyTap('Ctrl + F5')
			let chaxun = this.untils.waitImage("/input/1920/chaxun.png")
			if (chaxun) {
				this.pbottleRPA.browserCMD_click(`div[class="t-form__controls"] button[type="submit"]`)
			}

			const startTime = Date.now(); // 记录开始时间

			while (true) {
				this.pbottleRPA.sleep(5000); // 等待5秒，可以根据实际情况调整
				let tasksDownloaded = 0;
				console.log("当前用户名", myUsername)

				// 点击查询按钮
				this.pbottleRPA.browserCMD_click('div[class="t-form__controls"] button[type="submit"]');

				// 初始化一个数组来存储所有数据对象
				const dataArray = [];

				// 循环处理每个<tr>元素
				for (let n = 1; n <= 10; n++) {
					const id = this.pbottleRPA.browserCMD_text(
						`div[class="t-table__content"] tbody tr:nth-child(${n}) td:nth-child(1)`).trim();
					const taskname = this.pbottleRPA.browserCMD_text(
						`div[class="t-table__content"] tbody tr:nth-child(${n}) td:nth-child(2)`).trim();
					const submitter = this.pbottleRPA.browserCMD_text(
							`div[class="t-table__content"] tbody tr:nth-child(${n}) td:nth-child(3)`)
						.trim();
					const status = this.pbottleRPA.browserCMD_text(
							`div[class="t-table__content"] tbody tr:nth-child(${n}) td:nth-child(7)`)
						.trim();
					const downloadButton = this.pbottleRPA.browserCMD_text(
						`div[class="t-table__content"] tbody tr:nth-child(${n}) td:nth-child(11)`).trim();
					console.log("00000000000", id, submitter, status, downloadButton)
					// 构建每条数据的对象
					const dataObject = {
						id: id,
						taskname: taskname,
						submitter: submitter,
						status: status,
						downloadButton: downloadButton
					};

					// 将数据对象推入数组
					dataArray.push(dataObject);
				}

				// 输出结果数组
				console.log(",..........", dataArray);

				// 打印结果
				dataArray.forEach(task => {
					console.log(
						`任务ID: ${task.id}, 任务名称：${task.taskname},提交者: ${task.submitter}, 状态: ${task.status}, 能否下载: ${task.downloadButton}`
					);
				});

				// 检查所有任务是否都已经处理完成
				let tasksBySubmitter = dataArray.filter(task => task.submitter === myUsername);

				// 然后检查这些任务是否都满足条件
				let allTasksDownloaded = tasksBySubmitter.every(task => {
					return task.status === '处理完成' && task.downloadButton.includes('下载');
				});

				// 如果所有任务都已处理完成，结束循环
				if (allTasksDownloaded) {
					// 遍历任务列表，下载处理完成的任务
					for (let task1 of dataArray) {
						if (tasksDownloaded < count && task1.submitter === myUsername && task1.status === "处理完成" &&
							task1
							.downloadButton
							.includes("下载")) {
							console.log(`尝试下载任务 ${task1.id}`);

							// 实际下载操作
							this.pbottleRPA.browserCMD_click(
								`div[class="t-table__content"] tr:nth-child(${task1.id}) span[class="operate"]`);
							tasksDownloaded++;
							console.log(`tasksDownloaded`, tasksDownloaded);
						}
					}
					break;
				}
			}

			const currentTime = Date.now();
			const elapsedTime = Math.floor((currentTime - startTime) / 1000); // 计算已经过去的时间（秒）
			if (elapsedTime >= 900) {
				console.log("时间太长了")
				let res = this.untils.completeTask(
					this.childrenTask.id,
					this.childrenTask.flowId, 
					this.childrenTask.flowKey, 
					this.childrenTask.taskKey,
					this.childrenTask.variable, 
					1, 
					"提交发票超时"
				)
				console.log('完成结果', res)
			}
		}

		function formatDate(date) {
			var year = date.getFullYear();
			var month = padZero(date.getMonth() + 1);
			var day = padZero(date.getDate());
			return year + '-' + month + '-' + day;
		}

		// 给小于10的数字前面补0
		function padZero(num) {
			return num < 10 ? '0' + num : '' + num;
		}

		await this.untils.checkDownloadedFiles();

		let file_path = global_download_path + '/../dataRPA.zip';
		// let file = await this.untils.uploadFile(0)
		this.pbottleRPA.sleep(3000)
		let file = await this.untils.uploadToOSS(file_path)
		this.untils.addLog(global.traceId, `${file}`, 'gather_invoice')
		console.log('file', file)
		//老猫本地
		// let path = this.untils.httpfp('POST', 'callback/rpa/analysis', {
		// 	'collectType': 1,
		// 	'fileUrl': file,
		// 	'requestId': this.childrenTask.requestId

		// }, null, false)
		let path = ''
		try{
			 path = this.untils.httpfp('POST', 'taxmanage/callback/rpa/analysis', {
				'collectType': 1,
				'fileUrl': file,
				'requestId': this.childrenTask.requestId
			}, null, false)
			this.untils.addLog(global.traceId, `${JSON.stringify(path)}`, 'gather_invoice')
		}catch(error){
			this.untils.addLog(global.traceId, `${error.message}`, 'gather_invoice')
			console.error(error.message);
		}

		if (path.code == 500) {
			console.log('path------------', path)
			let res = this.untils.terminateTask(
				this.childrenTask.flowId,
				3,
				this.childrenTask.variable,
				'发票解析失败'
			)
			this.pbottleRPA.exit('发票解析失败:' + this.childrenTask.name)
		}

		let res = this.untils.completeTask(
			this.childrenTask.id, 
			this.childrenTask.flowId, 
			this.childrenTask.flowKey, 
			this.childrenTask.taskKey,
			this.childrenTask.variable, 
			1, 
			"提交发票成功"
		)
		if (res.state == 'fail') {
			this.pbottleRPA.exit('子任务提交失败:' + this.childrenTask.name)
		}
		return
	}


	async getcookie(proItem){
		/*
		* 发票采集之前，获取Cookie传给后台--------lufei
		* */
		let cookie = ''
		let cookie1 = ''
		let cookie2 = ''
		let cookie3 = ''
		let cookie4 = ''
		let rs = this.untils.waitImage("/input/1920/swszzh.png")
		this.pbottleRPA.moveAndClick(rs.x, rs.y)
		let error =  this.pbottleRPA.browserCMD_text(`div[class="t-message t-is-error"]`)
		console.log('error:',error)
		if(error == 'ok' ){
			this.pbottleRPA.sleep(5000)
			let CSQYMC = this.pbottleRPA.browserCMD_text(`div[class="title"]`)
			console.log(CSQYMC)
			if(CSQYMC !== 'ok'){
				// 先通过代码获取Cookie
				let getCookiesFromRPA = getCookie(proItem)
				cookie1 = getCookiesFromRPA.cookie1
				cookie2 = getCookiesFromRPA.cookie2
				console.log(cookie1,cookie2)
				if(cookie1 && cookie2 ){
					console.log("由小瓶获取Cookie")
					cookie3 = pbottleRPA.browserCMD_cookie(`${cookie1}`)
					cookie4 = pbottleRPA.browserCMD_cookie(`${cookie2}`)
					console.log(cookie3,cookie4)
					cookie = `${cookie1}=${cookie3}; ${cookie2}=${cookie4}`
					console.log('由小瓶获取的cookie',cookie)
					this.untils.addLog(global.traceId, `由小瓶获取的cookie：${cookie}`, 'getcookie')
				}else  {
					console.log("由控制台获取Cookie")
					this.pbottleRPA.sleep(1000)
					this.pbottleRPA.keyTap('Ctrl + Shift + J')
					this.pbottleRPA.paste(`允许粘贴`)
					this.pbottleRPA.sleep(1000)
					this.pbottleRPA.keyTap('ENTER')
					this.pbottleRPA.sleep(1000)
					this.pbottleRPA.paste(`let result = document.cookie; console.log(result); copy(result)`)
					this.pbottleRPA.keyTap('ENTER')
					// console.log(pbottleRPA.getClipboard())
					cookie = this.pbottleRPA.getClipboard()
					console.log('由控制台获取的cookie',cookie)
					this.untils.addLog(global.traceId, `由控制台获取的cookie：${cookie}`, 'getcookie')
					this.pbottleRPA.keyTap('ctrl+W')
				}
			}else {
				this.untils.addLog(global.traceId, 'Cookie获取失败！', 'getcookie')
				console.log("Cookie获取失败！",cookie)
			}
			
		}else{
			this.untils.addLog(global.traceId, `${error}`, 'getcookie')
			console.log('error:',error)
		}

		try {
			console.log('dq',this.proItem.url)
			//老猫本地
			// let getcookie = this.untils.httpfp('POST', 'callback/rpa/collectFinance', {
			// 	'cookie': cookie,
			// 	'requestId': this.childrenTask.requestId,
			// 	'dq':this.proItem.url
			// }, null, false)

			let getcookie = this.untils.httpfp('POST', 'taxmanage/callback/rpa/collectFinance', {
				'cookie': cookie,
				'requestId': this.childrenTask.requestId,
				'dq':this.proItem.url
			}, null, false)
			this.untils.addLog(global.traceId, `${JSON.stringify(getcookie)}`, 'getcookie')
			console.log('getcookie11111', getcookie)

			if (getcookie.code == 500) {
				console.log('getcookie------------', getcookie)
				this.pbottleRPA.exit('子任务提交失败:' + this.childrenTask.name)
			}
		}catch (error){
			this.untils.addLog(global.traceId, `${error.message}`, 'getcookie')
			console.error(error.message);
		}
		return cookie;
	}

	// 选择登录身份
	select_sf() {
		// value=03：办税员   value=05 管理员  
		let select = this.untils.isImage("/input/1920/sflx.png", 5)
		if (select) {
			this.pbottleRPA.browserCMD_click(`div[aria-label="身份类型选择"] button span`)
			this.pbottleRPA.sleep(3000)
			let res = this.untils.completeTask(
				this.childrenTask.id, 
				this.childrenTask.flowId, 
				this.childrenTask.flowKey, 
				this.childrenTask.taskKey,
				this.childrenTask.variable, 
				1, 
				"选择身份成功"
			)
		} else {
			this.pbottleRPA.browserCMD_click(`div[aria-label="身份类型选择"] button span`)
			this.pbottleRPA.sleep(3000)
			console.log('没找到该图片2')
			let res = this.untils.completeTask(
				this.childrenTask.id, 
				this.childrenTask.flowId, 
				this.childrenTask.flowKey, 
				this.childrenTask.taskKey, 
				this.childrenTask.variable, 
				1, 
				"无需选择身份"
			)
		}
		return 1
	}
}

module.exports = ExtendClass;
