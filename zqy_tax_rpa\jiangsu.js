const Untils = require('./untils')
const request = require('sync-request'); //默认同步请求
const pbottleRPA = require('./pbottleRPA')
const xlsx = require("node-xlsx");
const fs = require('fs')
const php = require("php2javascript");
const {
	FormData
} = require('sync-request');
const ExcelJS = require('exceljs');
const path = require('path');
const compressing = require('compressing');
const workSheetsFromFile = xlsx.parse(`${__dirname}\\配置项.xlsx`);
const config_sheet = workSheetsFromFile[0].data;
const global_download_path = config_sheet[1][2];
class ExtendClass {
	constructor(proItem, pbottleRPA, childrenTask, global_access_token) {

		this.proItem = proItem; // 将传入的值存储在类的属性中
		this.childrenTask = childrenTask; // 将传入的值存储在类的属性中
		this.pbottleRPA = pbottleRPA
		this.untils = new Untils(global_access_token)
	}

	rpa_to_login() {
		this.pbottleRPA.keyTap('Ctrl + Shift + W');
		let url = `https://etax.${this.proItem.url}.chinatax.gov.cn/sso/login`
		let res1 = request('GET', url);
		var regex = /var kxUrl = "([^"]+)"/;
		const matchResult = res1.body.toString().match(regex); // 将响应体转换为字符串
		if (matchResult && matchResult.length > 1) {
			const kxUrl = matchResult[1];
			console.log(kxUrl); // 输出提取到的 kxUrl
			this.pbottleRPA.openURL(kxUrl)
			this.pbottleRPA.keyTap('alt+space')
			this.pbottleRPA.keyTap('x') // 浏览器最大化
		} else {
			console.log('未找到 kxUrl');
		}

		let ready = `input[placeholder="统一社会信用代码/纳税人识别号"]`
		if (ready) {
			let res = this.untils.completeTask(this.childrenTask.taskId, 1, "进入登录页成功")
			console.log('完成RPA登录税局节点', res)
		} else {
			let res = this.untils.completeTask(this.childrenTask.taskId, 0, "进入登录页失败")
			console.log('完成RPA登录税局节点', res)
		}
		return
	}

	// 二维码登录进入二维码登录页
	rpa_to_codelogin() {
		this.pbottleRPA.keyTap('Ctrl + Shift + W');
		let url = `https://etax.${this.proItem.url}.chinatax.gov.cn/sso/login`
		let res1 = request('GET', url);
		var regex = /var kxUrl = "([^"]+)"/;
		const matchResult = res1.body.toString().match(regex); // 将响应体转换为字符串
		if (matchResult && matchResult.length > 1) {
			const kxUrl = matchResult[1];
			console.log(kxUrl); // 输出提取到的 kxUrl
			this.pbottleRPA.openURL(kxUrl)
			this.pbottleRPA.keyTap('alt+space')
			this.pbottleRPA.keyTap('x') // 浏览器最大化
		} else {
			console.log('未找到 kxUrl');
		}

		let ready = `input[placeholder="统一社会信用代码/纳税人识别号"]`
		if (ready) {
			let res = this.untils.completeTask(this.childrenTask.taskId, 1, "进入登录页成功")
			console.log('完成RPA登录税局节点', res)
		} else {
			let res = this.untils.completeTask(this.childrenTask.taskId, 0, "进入登录页失败")
			console.log('完成RPA登录税局节点', res)
		}
		return
	}

	// 选择登录身份
	select_sf() {
		// value=03：办税员   value=05 管理员  
		let select = this.untils.isImage("/input/1920/sflx.png", 5)
		if (select) {
			this.pbottleRPA.browserCMD_click(`div[aria-label="身份类型选择"] button span`)
			this.pbottleRPA.sleep(3000)
			let res = this.untils.completeTask(this.childrenTask.taskId, 1, "选择身份成功")
		} else {
			this.pbottleRPA.browserCMD_click(`div[aria-label="身份类型选择"] button span`)
			this.pbottleRPA.sleep(3000)
			console.log('没找到该图片2')
			let res = this.untils.completeTask(this.childrenTask.taskId, 1, "无需选择身份")
		}
		return
	}

	// 检查登录身份权限
	async check_sf() {
		let sfinfo = "";
		for (let index = 0; index < 100; index++) {
			this.pbottleRPA.sleep(100);
			sfinfo = this.pbottleRPA.browserCMD_text('#div_user_info > div > div > div:nth-child(5) b');
			if (sfinfo != "ok") {
				break;
			}
		}
		console.log("sfinfo", sfinfo);
		let value = sfinfo.replace(/\s/g, "");

		if (value.includes("法定") || value.includes("财务") || value.includes("")) {
			console.log("当前登录人员有权限添加用户")
			let res = this.untils.completeTask(this.childrenTask.taskId, 1, "有权限添加用户")
		} else {
			let rollBackRes = this.untils.http("POST", 'api/blade-workflow/app/task/rollbackTask', {
				'taskId': this.childrenTask.taskId,
				"nodeId": "selectLogin",
				"comment": "该账号无权限添加用户，请更换法人账号或者财务管理员账号",
				"rejecter": "check_sf"
			}, null, false)
			console.log('rollBackRes', rollBackRes)
			return
		}
	}


	api_userpass() {
		this.pbottleRPA.keyTap('Ctrl + Shift + W');
		let url = `https://etax.${this.proItem.url}.chinatax.gov.cn/sso/login`
		let res = request('GET', url);
		console.log(res);
		var regex = /var kxUrl = "([^"]+)"/;
		const matchResult = res.body.toString().match(regex); // 将响应体转换为字符串
		if (matchResult && matchResult.length > 1) {
			const kxUrl = matchResult[1];
			console.log(kxUrl); // 输出提取到的 kxUrl
			this.pbottleRPA.openURL(kxUrl)
		} else {
			console.log('未找到 kxUrl');
		}

		let ready = this.untils.waitImage("/input/1920/loginready.png")
		this.pbottleRPA.moveMouseSmooth(ready.x, ready.y)
		this.pbottleRPA.mouseClick() //focus
		console.log("this.childrenTask.variables.nsrsbh", this.childrenTask.variables.nsrsbh)
		console.log("this.childrenTask.variables.telX", this.childrenTask.variables.telX)
		console.log("this.childrenTask.variables.bsymm", this.childrenTask.variables.bsymm)
		this.pbottleRPA.paste(this.childrenTask.variables.nsrsbh)
		this.pbottleRPA.keyTap('tab')
		this.pbottleRPA.paste(this.childrenTask.variables.telX)
		this.pbottleRPA.keyTap('tab')
		this.pbottleRPA.paste(this.childrenTask.variables.bsymm)
		this.pbottleRPA.keyTap('tab')

		this.pbottleRPA.moveMouse(ready.x - 153, ready.y + 180)
		this.pbottleRPA.mouseLeftDragTo(ready.x - 153 + 370, ready.y + 180)
		this.pbottleRPA.browserCMD_click(`button span:contains(登录)`)
		let value = this.untils.checkLogin()
		if (value.result == 0) {
			let res = this.untils.completeTask(this.childrenTask.taskId, value.result, value.message)
			console.log('获取接口参数输入账密节点', res)
		} else {
			let res = this.untils.completeTask(this.childrenTask.taskId, value.result, value.message)
			console.log('获取接口参数输入账密节点', res)
		}
		return {}
	}


	// gather_taxpayer() {
	// 	this.pbottleRPA.openURL(`https://etax.${this.proItem.url}.chinatax.gov.cn:8443/loginb/`)
	//
	// 	let rs = this.untils.waitImage("/input/jiangsu/swszzh.png")
	// 	this.pbottleRPA.moveAndClick(rs.x, rs.y)
	//
	// 	this.pbottleRPA.sleep(3000)
	// 	const date = new Date();
	// 	const timestamp = date.getTime();
	// 	let url = `https://etax.${this.proItem.url}.chinatax.gov.cn:8443/szzh/zhcx/nsrxxcx?ruuid=${timestamp}`
	// 	this.pbottleRPA.openURL(url)
	// 	let zgxx = this.untils.waitImage("/input/jiangsu/zgxx.png")
	// 	if (zgxx) {
	// 		this.pbottleRPA.browserCMD_click('span:contains(资格信息查询)')
	// 	} else {
	// 		this.pbottleRPA.browserCMD_click('span:contains(资格信息查询)')
	// 	}
	//
	// 	var page = this.pbottleRPA.browserCMD_text(
	// 		'div[class="sfzrdxx"] div.t-table__pagination div.t-pagination__total')
	// 	var numbers = page.match(/\d+/g);
	// 	if (numbers) {
	// 		numbers.forEach(number => console.log(number));
	// 		console.log("page:", numbers)
	// 	} else {
	// 		console.log("未找到page")
	// 	}
	//
	// 	var NSRZG = this.pbottleRPA.browserCMD_text(
	// 		`div[class="sfzrdxx"] table  tbody tr:nth-child(${numbers}) td:nth-child(2)`)
	// 	var RDRQ = this.pbottleRPA.browserCMD_text(
	// 		`div[class="sfzrdxx"] table  tbody tr:nth-child(${numbers}) td:nth-child(6)`)
	// 	var YXQ_Q = this.pbottleRPA.browserCMD_text(
	// 		`div[class="sfzrdxx"] table  tbody tr:nth-child(${numbers}) td:nth-child(3)`)
	// 	var YXQ_Z = this.pbottleRPA.browserCMD_text(
	// 		`div[class="sfzrdxx"] table  tbody tr:nth-child(${numbers}) td:nth-child(4)`)
	//
	// 	console.log(NSRZG, RDRQ, YXQ_Q, YXQ_Z);
	//
	//
	// 	//资格信息查询------lufei
	//
	// 	let zgxxcx = this.pbottleRPA.browserCMD_html(`div [class="sfzrdxx"]`)
	// 	this.untils.getFs().writeFileSync(global_download_path + `/zgxxcx.html`, zgxxcx)
	//
	// 	// 完成子任务
	// 	let json = this.untils.http('POST', 'api/blade-workflow/app/task/rpaCompleteTask', {
	// 		'taskId': this.childrenTask.taskId,
	// 		"pass": true,
	// 		"variables": {
	// 			"taxpayerName": NSRZG,
	// 			"taxpayerDate": RDRQ,
	// 			"taxpayerStartDate": YXQ_Q,
	// 			"taxpayerEndDate": YXQ_Z,
	// 		}
	// 	}, null, false)
	//
	// 	if (!json.success) {
	// 		this.pbottleRPA.exit('子任务提交失败:' + global_taskName)
	// 	}
	//
	// 	return
	// }

	async gather_tax() {
		// await this.process_taxVat()
		// await this.process_taxIncome()
		// await this.process_financial()
		// await this.process_taxLevy()
		// await this.process_taxRatLevle()
		// await this.process_taxPayerInfo()
		// await this.process_illegalInfo()
		// await this.process_taxArrearsInfo()
		// let file = await this.untils.uploadFile(0)
		// let path = this.untils.httpfp('POST', 'taxmanage/callback/rpa/analysis', {
		// 	'collectType': 2,
		// 	'fileUrl': file,
		// 	'requestId': this.childrenTask.variables.requestId
		//
		// }, null, false)

		if (path.code == 500) {
			console.log('path------------', path)
			this.pbottleRPA.exit('子任务提交失败:' + global_taskName)
		}

		let res = this.untils.completeTask(this.childrenTask.taskId, 1, "税务下载成功")
		console.log('完成RPA登录税局节点', res)

		return
	}

	/**
	 * 增值税
	 */
	async process_taxVat() {
		function formatDate(date) {
			var year = date.getFullYear();
			var month = padZero(date.getMonth() + 1);
			var day = padZero(date.getDate());
			return year + '-' + month + '-' + day;
		}

		// 给小于10的数字前面补0
		function padZero(num) {
			return num < 10 ? '0' + num : '' + num;
		}

		console.log("开始下载增值税")
		this.pbottleRPA.openURL(`https://etax.jiangsu.chinatax.gov.cn:8443/loginb/`)
		this.pbottleRPA.sleep(3 * 1000)
		let rs = this.untils.waitImage("/input/jiangsu/swszzh.png")
		this.pbottleRPA.moveAndClick(rs.x, rs.y)
		this.pbottleRPA.sleep(3000)
		let type = this.pbottleRPA.browserCMD_text(` div.companyTitle > div:nth-child(2) > div > span:nth-child(1)`)

		this.pbottleRPA.openURL(
			`https://etax.jiangsu.chinatax.gov.cn:8443/szzh/zhcx/sbxx`
		)
		this.pbottleRPA.sleep(5000)
		this.pbottleRPA.browserCMD_click(
			` div.search-icon > div:nth-child(1) > div.search-title`
		)
		try {
			let found = false; // 用于标记是否找到图片
			for (let index = 0; index < 30; index++) {
				this.pbottleRPA.sleep(1000)
				let sbxx = this.untils.existImage("/input/1920/sbxx.png")
				if (sbxx === false) {
					this.pbottleRPA.keyTap('F5')
					continue;
				}
				this.pbottleRPA.moveMouseSmooth(sbxx.x, sbxx.y)
				// 获取当前日期
				var currentDate = new Date();

				this.pbottleRPA.browserCMD_click(
					`div > div > div:nth-child(6) > div > div.t-form__controls > div > div > div > div > div > input`
				)
				this.pbottleRPA.browserCMD_val(
					`div > div > div:nth-child(6) > div > div.t-form__controls > div > div > div > div > div > input`,
					'');
				this.pbottleRPA.keyTap('Enter')

				this.pbottleRPA.browserCMD_click(
					`div > div > div:nth-child(7) > div > div.t-form__controls > div > div > div > div > div > input`
				)
				this.pbottleRPA.browserCMD_val(
					`div > div > div:nth-child(7) > div > div.t-form__controls > div > div > div > div > div > input`,
					'');
				this.pbottleRPA.keyTap('Enter')

				if (type.includes('一般')) {
					// 获取当前日期的上一个月
					currentDate.setMonth(currentDate.getMonth() - 1);

					// 循环输出36个月的数据
					for (var i = 1; i <= 36; i++) {
						// 获取12个月前的日期
						var startDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - 0, 1);

						// 获取当前月份的最后一天
						var endDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);

						// 输出日期范围
						console.log(formatDate(startDate) + '---------->' + formatDate(
							endDate));

						// 将当前日期往前推1个月
						currentDate.setMonth(currentDate.getMonth() - 1);

						this.pbottleRPA.browserCMD_click(
							`div:nth-child(4) > div > div.t-form__controls.t-is-success > div > div > div > div > div > input`
						)
						this.pbottleRPA.browserCMD_val(
							`div:nth-child(4) > div > div.t-form__controls.t-is-success > div > div > div > div > div > input`,
							formatDate(startDate));
						this.pbottleRPA.keyTap('Enter')

						this.pbottleRPA.browserCMD_click(
							`div:nth-child(5) > div > div.t-form__controls > div > div > div > div > div > input`
						)
						this.pbottleRPA.browserCMD_val(
							`div:nth-child(5) > div > div.t-form__controls > div > div > div > div > div > input`,
							formatDate(endDate));
						this.pbottleRPA.keyTap('Enter')

						this.pbottleRPA.browserCMD_click(`button span:contains(查询)`)

						// 依次下载申报报表中内容
						this.untils.zzsDown();
					}

				} else {
					// 输出季度数据
					if (currentDate.getMonth() < 3) {
						currentDate.setFullYear(currentDate.getFullYear() - 1, 11, 31); // 如果是1月到3月，调整为前年的12月31日
					} else {
						currentDate.setMonth(currentDate.getMonth() - 1); // 否则调整为上个月
					}

					for (var i = 1; i <= 16; i++) {
						// 计算当前季度的起止日期
						var quarterStartMonth = Math.floor(currentDate.getMonth() / 3) * 3; // 当前季度的开始月份
						var quarterEndMonth = quarterStartMonth + 2; // 当前季度的结束月份

						var startDate = new Date(currentDate.getFullYear(), quarterStartMonth, 1);
						var endDate = new Date(currentDate.getFullYear(), quarterEndMonth + 1, 0); // 获取季度最后一天

						console.log(formatDate(startDate) + ' ----------> ' + formatDate(endDate));

						currentDate.setMonth(currentDate.getMonth() - 3); // 每次向前推3个月

						this.pbottleRPA.browserCMD_click(
							`div:nth-child(4) > div > div.t-form__controls.t-is-success > div > div > div > div > div > input`
						)
						this.pbottleRPA.browserCMD_val(
							`div:nth-child(4) > div > div.t-form__controls.t-is-success > div > div > div > div > div > input`,
							formatDate(startDate));
						this.pbottleRPA.keyTap('Enter')

						this.pbottleRPA.browserCMD_click(
							`div:nth-child(5) > div > div.t-form__controls > div > div > div > div > div > input`
						)
						this.pbottleRPA.browserCMD_val(
							`div:nth-child(5) > div > div.t-form__controls > div > div > div > div > div > input`,
							formatDate(endDate));
						this.pbottleRPA.keyTap('Enter')

						this.pbottleRPA.browserCMD_click(`button span:contains(查询)`)

						// 依次下载申报报表中内容
						this.untils.zzsDown();
					}
				}

				found = true;
				break;
			}
			if (!found) {
				throw new Error('增值税图片未找到，操作失败');
			}
		} catch (error) {
			console.error(error.message);
		}

	}

	/**
	 * 企业所得税
	 */
	async process_taxIncome() {
		function formatDate(date) {
			var year = date.getFullYear();
			var month = padZero(date.getMonth() + 1);
			var day = padZero(date.getDate());
			return year + '-' + month + '-' + day;
		}

		// 给小于10的数字前面补0
		function padZero(num) {
			return num < 10 ? '0' + num : '' + num;
		}
		console.log("开始下载所得税")
		this.pbottleRPA.openURL(
			`https://etax.jiangsu.chinatax.gov.cn:8443/szzh/zhcx/sbxx`
		)
		this.pbottleRPA.sleep(5000)
		this.pbottleRPA.browserCMD_click(
			` div.search-icon > div:nth-child(1) > div.search-title`
		)
		try {
			let found = false; // 用于标记是否找到图片
			for (let index = 0; index < 30; index++) {
				this.pbottleRPA.sleep(1000)
				let sbxx = this.untils.existImage("/input/1920/sbxx.png")
				if (sbxx === false) {
					this.pbottleRPA.keyTap('F5')
					continue;
				}

				this.pbottleRPA.moveMouseSmooth(sbxx.x, sbxx.y)

				// 获取当前日期
				var currentDate = new Date();

				// 循环输出3年的数据
				for (var i = 1; i <= 4; i++) {
					this.pbottleRPA.keyTap('F5')
					this.pbottleRPA.sleep(1000)
					let sbxx = this.untils.existImage("/input/1920/sbxx.png")
					if (sbxx === false) {
						this.pbottleRPA.keyTap('F5')
						continue;
					}
					// 获取12个月前的日期
					var startDate = new Date(currentDate.getFullYear(), 0, 1);

					// 获取当前月份的最后一天
					var endDate = new Date(currentDate.getFullYear(), 11, 31);

					// 输出日期范围
					console.log(formatDate(startDate) + '---------->' + formatDate(
						endDate));

					// 将当前日期往前推1年
					currentDate.setFullYear(currentDate.getFullYear() - 1)

					this.pbottleRPA.browserCMD_click(
						`div:nth-child(4) > div > div.t-form__controls.t-is-success > div > div > div > div > div > input`
					)
					this.pbottleRPA.browserCMD_val(
						`div:nth-child(4) > div > div.t-form__controls.t-is-success > div > div > div > div > div > input`,
						formatDate(startDate));
					this.pbottleRPA.keyTap('Enter')

					this.pbottleRPA.browserCMD_click(
						`div:nth-child(5) > div > div.t-form__controls > div > div > div > div > div > input`)
					this.pbottleRPA.browserCMD_val(
						`div:nth-child(5) > div > div.t-form__controls > div > div > div > div > div > input`,
						formatDate(endDate));
					this.pbottleRPA.keyTap('Enter')

					this.pbottleRPA.browserCMD_click(
						`div:nth-child(6) > div > div.t-form__controls.t-is-success > div > div > div > div > div > input`
					)
					this.pbottleRPA.browserCMD_val(
						`div:nth-child(6) > div > div.t-form__controls.t-is-success > div > div > div > div > div > input`,
						'');
					this.pbottleRPA.keyTap('Enter')

					this.pbottleRPA.browserCMD_click(
						`div:nth-child(7) > div > div.t-form__controls.t-is-success > div > div > div > div > div > input`
					)
					this.pbottleRPA.browserCMD_val(
						`div:nth-child(7) > div > div.t-form__controls.t-is-success > div > div > div > div > div > input`,
						'');
					this.pbottleRPA.keyTap('Enter')

					this.pbottleRPA.browserCMD_click(`button span:contains(查询)`)

					// 依次下载申报报表中内容
					this.untils.sdsDown();
				}

				found = true;
				break;
			}
			if (!found) {
				throw new Error('所得税图片未找到，操作失败');
			}
		} catch (error) {
			console.error(error.message);
		}
	}

	/**
	 * 财务报表
	 */
	async process_financial() {
		function formatDate(date) {
			var year = date.getFullYear();
			var month = padZero(date.getMonth() + 1);
			var day = padZero(date.getDate());
			return year + '-' + month + '-' + day;
		}

		// 给小于10的数字前面补0
		function padZero(num) {
			return num < 10 ? '0' + num : '' + num;
		}
		console.log("开始下载财务报表")
		this.pbottleRPA.openURL('https://etax.jiangsu.chinatax.gov.cn:8443/szc/szzh/sbss/view/#/zhcx/cwbbxxcx')
		try {
			let found = false; // 用于标记是否找到图片
			for (let index = 0; index < 30; index++) {
				this.pbottleRPA.sleep(1000)
				let cwbb = this.untils.existImage("/input/1920/cwbb.png")
				if (cwbb === false) {
					this.pbottleRPA.keyTap('F5')
					continue;
				}

				this.pbottleRPA.browserCMD_click(
					`body > section > section > section > main > div > div > form > div > div:nth-child(5) input`
				)
				this.pbottleRPA.browserCMD_val(
					'body > section > section > section > main > div > div > form > div > div:nth-child(5) input',
					'');
				this.pbottleRPA.keyTap('Enter')

				this.pbottleRPA.browserCMD_click(
					`body > section > section > section > main > div > div > form > div > div:nth-child(4) input`
				)
				this.pbottleRPA.browserCMD_val(
					'body > section > section > section > main > div > div > form > div > div:nth-child(4) input',
					'');
				this.pbottleRPA.keyTap('Enter')

				// 获取当前日期
				var currentDate = new Date();

				// 循环输出3年的数据
				for (var i = 1; i <= 4; i++) {

					// 获取当前月份的最后一天
					var startDate = new Date(currentDate.getFullYear(), 0, 1);
					console.log("endDate:", startDate)

					// 获取12个月前的日期
					var endDate = new Date(currentDate.getFullYear(), 11, 31);
					console.log("startDate:", endDate)


					// 输出日期范围
					console.log(formatDate(startDate) + '---------->' + formatDate(
						endDate));

					//将日期往前推1年
					currentDate.setFullYear(currentDate.getFullYear() - 1)

					this.pbottleRPA.browserCMD_click(
						`body > section > section > section > main > div > div > form > div > div:nth-child(3) input`
					)
					this.pbottleRPA.browserCMD_val(
						'body > section > section > section > main > div > div > form > div > div:nth-child(3) input',
						formatDate(endDate));
					this.pbottleRPA.keyTap('Enter')

					this.pbottleRPA.browserCMD_click(
						`body > section > section > section > main > div > div > form > div > div:nth-child(2) input`
					)
					this.pbottleRPA.browserCMD_val(
						'body > section > section > section > main > div > div > form > div > div:nth-child(2) input',
						formatDate(startDate));
					this.pbottleRPA.keyTap('Enter')

					this.pbottleRPA.browserCMD_click(`button span:contains(查询)`)

					// 依次下载财务报表中内容
					this.untils.clickAndDown()
				}

				found = true;
				break;
			}
			if (!found) {
				throw new Error('财务报表图片未找到，操作失败');
			}
		} catch (error) {
			console.error(error.message);
		}
	}


	/**
	 * 征收信息
	 */
	async process_taxLevy() {
		function formatDate(date) {
			var year = date.getFullYear();
			var month = padZero(date.getMonth() + 1);
			var day = padZero(date.getDate());
			return year + '-' + month + '-' + day;
		}

		// 给小于10的数字前面补0
		function padZero(num) {
			return num < 10 ? '0' + num : '' + num;
		}
		console.log("开始下载征收信息")
		this.pbottleRPA.openURL('https://etax.jiangsu.chinatax.gov.cn:8443/szzh/zhcx/jkxx/jkxxcx')
		this.pbottleRPA.sleep(2000)
		try {
			let found = false; // 用于标记是否找到图片
			for (let index = 0; index < 10; index++) {
				let rs = this.untils.existImage("/input/1920/jkxxcx.png")
				if (rs === false) {
					this.pbottleRPA.keyTap('F5')
					this.pbottleRPA.sleep(1000)
					continue;
				}

				// 获取当前日期
				var currentDate = new Date();

				// 循环输出36个月的数据
				for (var i = 1; i <= 4; i++) {
					// 获取12个月前的日期
					var startDate = new Date(currentDate.getFullYear(), 0, 1);

					// 获取当前月份的最后一天
					var endDate = new Date(currentDate.getFullYear(), 11, 31);

					// 输出日期范围
					console.log(formatDate(startDate) + '---------->' + formatDate(
						endDate));

					//将日期往前推1年
					currentDate.setFullYear(currentDate.getFullYear() - 1)

					this.pbottleRPA.browserCMD_click(
						`form > div > div > div > div > div:nth-child(5) > div > div.t-form__controls > div > div > div > div > div > input`
					)
					this.pbottleRPA.browserCMD_val(
						'form > div > div > div > div > div:nth-child(5) > div > div.t-form__controls > div > div > div > div > div > input',
						formatDate(endDate));
					this.pbottleRPA.keyTap('Enter')

					this.pbottleRPA.browserCMD_click(
						`form > div > div > div > div > div:nth-child(4) > div > div.t-form__controls > div > div > div > div > div > input`
					)
					this.pbottleRPA.browserCMD_val(
						'form > div > div > div > div > div:nth-child(4) > div > div.t-form__controls > div > div > div > div > div > input',
						formatDate(startDate));
					this.pbottleRPA.keyTap('Enter')

					this.pbottleRPA.browserCMD_click(`button span:contains(查询)`)

					// 依次获税款征收信息
					this.untils.skzsxxDown(formatDate(startDate), formatDate(endDate))
				}

				found = true;
				break;
			}
			if (!found) {
				throw new Error('财务报表图片未找到，操作失败');
			}
		} catch (error) {
			console.error(error.message);
		}

		//清理窗口
		this.pbottleRPA.keyTap('ctrl+w')
	}

	/**
	 * 纳税信用评价等级
	 */
	async process_taxRatLevle() {
		console.log("开始下载纳税信用评价等级")
		this.pbottleRPA.openURL(`https://etax.jiangsu.chinatax.gov.cn:8443/loginb/`)
		this.pbottleRPA.sleep(1000)
		this.pbottleRPA.openURL(
			`https://etax.jiangsu.chinatax.gov.cn:8443/ssjg/view/zhssjg/#/nsxxgl/index?reforigin=2`)
		this.pbottleRPA.sleep(3000)
		try {
			let found = false; // 用于标记是否找到图片
			for (let index = 0; index < 10; index++) {
				let pingjianiandu = this.untils.existImage("/input/1920/pingjianiandu.png")
				if (pingjianiandu === false) {
					this.pbottleRPA.sleep(1000)
					this.pbottleRPA.keyTap('F5')
					continue;
				}
				var currentDate = new Date();
				for (let index = 0; index < 3; index++) {
					var year = currentDate.getFullYear() - 1
					console.log("-----", year)

					this.pbottleRPA.browserCMD_click(`input[placeholder="请选择年份"]`)
					this.pbottleRPA.browserCMD_val('input[placeholder="请选择年份"]', currentDate.getFullYear() - 1);
					this.pbottleRPA.keyTap('Enter')
					let pj = this.untils.existImage("/input/1920/pjxxcj.png")
					if (pj === true) {
						this.pbottleRPA.browserCMD_click(`div[class="nsxxglcx-pjnd"] span:contains(评价信息出具)`)
						this.pbottleRPA.sleep(3000)
						var iframesrc = this.pbottleRPA.browserCMD_attr('iframe[frameborder="0"]', 'src')
						await this.pbottleRPA.openURL("https://etax.jiangsu.chinatax.gov.cn:8443" + iframesrc)
						this.pbottleRPA.sleep(500)
						
						this.pbottleRPA.browserCMD_click(`button[class="t-button--theme-primary"]`)
						this.pbottleRPA.sleep(3000)
						this.pbottleRPA.browserCMD_click(`button[id="download"]`)
						this.pbottleRPA.keyTap('Ctrl +W')
					}

					currentDate.setFullYear(currentDate.getFullYear() - 1)
		
				}
				found = true;
				break;

				if (!found) {
					throw new Error('纳税信用图片未找到，操作失败');
				}
			}
		} catch (error) {
			console.error(error.message);
		}
	}

	/**
	 * 纳税人信息
	 */
	async process_taxPayerInfo() {
		console.log("开始下载纳税人信息")
		this.pbottleRPA.openURL(`https://etax.jiangsu.chinatax.gov.cn:8443/loginb/`)
		this.pbottleRPA.sleep(3 * 1000)
		let rs = this.untils.waitImage("/input/jiangsu/swszzh.png")
		this.pbottleRPA.moveAndClick(rs.x, rs.y)
		this.pbottleRPA.sleep(3000)
		const date = new Date();
		const timestamp = date.getTime();
		this.pbottleRPA.openURL(`https://etax.jiangsu.chinatax.gov.cn:8443/szzh/zhcx/nsrxxcx?ruuid=${timestamp}`)
		let zgxx = this.untils.existImage("/input/jiangsu/zgxx.png")
		if (zgxx === false) {
			this.pbottleRPA.browserCMD_click(`span:contains(基础信息)`)
		} else {
			this.pbottleRPA.browserCMD_click(`span:contains(基础信息)`)
		}

		this.pbottleRPA.sleep(500)
		let jcxx = this.pbottleRPA.browserCMD_html(`div [class="jcxx"]`)
		this.pbottleRPA.sleep(500)
		this.untils.getFs().writeFileSync(global_download_path + `/jcxx.html`, jcxx)

		this.pbottleRPA.browserCMD_click(`span:contains(注册经营信息)`)
		this.pbottleRPA.sleep(500)
		let zcjyxx = this.pbottleRPA.browserCMD_html(`div [class="jyzcxx"]`)
		this.pbottleRPA.sleep(500)
		this.untils.getFs().writeFileSync(global_download_path + `/zcjyxx.html`, zcjyxx)

		this.pbottleRPA.browserCMD_click(`span:contains(企业经营信息)`)
		this.pbottleRPA.sleep(500)
		let qyjyxx = this.pbottleRPA.browserCMD_html(`div [class="qyjyxx"]`)
		this.pbottleRPA.sleep(500)
		this.untils.getFs().writeFileSync(global_download_path + `/qyjyxx.html`, qyjyxx)

		this.pbottleRPA.browserCMD_click(`span:contains(法定代表人关联企业信息)`)
		this.pbottleRPA.sleep(500)
		let fddbrglqyxx = this.pbottleRPA.browserCMD_html(`div [class="fddbrglqyxx"]`)
		this.pbottleRPA.sleep(500)
		this.untils.getFs().writeFileSync(global_download_path + `/fddbrglqyxx.html`, fddbrglqyxx)

		this.pbottleRPA.browserCMD_click(`span:contains(业主信息)`)
		this.pbottleRPA.sleep(500)
		let yzxx = this.pbottleRPA.browserCMD_html(`div [class="yzxx"]`)
		this.pbottleRPA.sleep(500)
		this.untils.getFs().writeFileSync(global_download_path + `/yzxx.html`, yzxx)

		this.pbottleRPA.browserCMD_click(`span:contains(投资方信息)`)
		this.pbottleRPA.sleep(500)
		let tzfxx = this.pbottleRPA.browserCMD_html(`div [class="tzfxx"]`)
		this.pbottleRPA.sleep(500)
		this.untils.getFs().writeFileSync(global_download_path + `/tzfxx.html`, tzfxx)

	}

	/**
	 * 纳税人违法违章信息
	 */
	async process_illegalInfo() {
		function formatDate(date) {
			var year = date.getFullYear();
			var month = padZero(date.getMonth() + 1);
			var day = padZero(date.getDate());
			return year + '-' + month + '-' + day;
		}

		// 给小于10的数字前面补0
		function padZero(num) {
			return num < 10 ? '0' + num : '' + num;
		}
		console.log("开始下载纳税人违法违章信息")
		this.pbottleRPA.openURL(`https://etax.jiangsu.chinatax.gov.cn:8443/loginb/`)
		this.pbottleRPA.sleep(3 * 1000)
		let rs = this.untils.waitImage("/input/jiangsu/swszzh.png")
		this.pbottleRPA.moveAndClick(rs.x, rs.y)
		this.pbottleRPA.sleep(3000)
		let wfcxjgurl = `https://etax.jiangsu.chinatax.gov.cn:8443/szzh/zhcx/wfwzxxcx`
		this.pbottleRPA.openURL(wfcxjgurl)

		let wfcxjg = this.untils.existImage("/input/1920/wfcxjg.png")
		if (wfcxjg === false) {
			this.pbottleRPA.keyTap('F5')
			this.pbottleRPA.sleep(3 * 1000)
		}

		var currentDate = new Date();
		currentDate.setMonth(currentDate.getMonth() - 1);

		// 获取60个月前的日期
		var startDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - 59, 1);

		// 获取当前月份的最后一天
		var endDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);

		this.pbottleRPA.browserCMD_click(
			`div[class="search-row"] div:nth-child(1) div div.t-form__controls  input`)
		this.pbottleRPA.browserCMD_val(
			'div[class="search-row"] div:nth-child(1) div div.t-form__controls  input',
			formatDate(startDate));
		this.pbottleRPA.sleep(500)
		this.pbottleRPA.keyTap('Enter')

		this.pbottleRPA.browserCMD_click(
			`div[class="search-row"] div:nth-child(2) div div.t-form__controls  input`)
		this.pbottleRPA.browserCMD_val(
			'div[class="search-row"] div:nth-child(2) div div.t-form__controls  input',
			formatDate(endDate));
		this.pbottleRPA.sleep(500)
		this.pbottleRPA.keyTap('Enter')

		this.pbottleRPA.browserCMD_click(
			`div[class="search-row"] div:nth-child(3) div div.t-form__controls  input`)
		this.pbottleRPA.browserCMD_click('li[title="全部"]');
		this.pbottleRPA.sleep(500)
		this.pbottleRPA.browserCMD_click(`div[class="search-row"] div:nth-child(4) button[type="submit"]`)

		let wfwzxx = this.pbottleRPA.browserCMD_html(`div[class="t-table__content"]`)
		this.untils.getFs().writeFileSync(global_download_path + `/wfwzxx.html`, wfwzxx)
	}

	/**
	 * 欠税信息
	 */
	async process_taxArrearsInfo() {
		console.log("开始下载欠税信息")
		this.pbottleRPA.openURL(`https://etax.jiangsu.chinatax.gov.cn:8443/loginb/`)
		this.pbottleRPA.sleep(3 * 1000)
		let rs = this.untils.waitImage("/input/jiangsu/swszzh.png")
		this.pbottleRPA.moveAndClick(rs.x, rs.y)
		this.pbottleRPA.sleep(3000)
		let qsxxcxurl = `https://etax.jiangsu.chinatax.gov.cn:8443/szzh/zhcx/qsxx/qsxxcx`
		this.pbottleRPA.openURL(qsxxcxurl)

		this.pbottleRPA.browserCMD_click(`div[class="search-row"] div:nth-child(4) button[type="submit"]`)
		this.pbottleRPA.browserCMD_click(`div[class="search"] div:nth-child(2) button`)
		this.pbottleRPA.sleep(3000)
	}


}

module.exports = ExtendClass;
