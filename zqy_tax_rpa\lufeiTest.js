const Untils = require('./untils')
const request = require('sync-request'); //默认同步请求
const pbottleRPA = require('./pbottleRPA')
const xlsx = require("node-xlsx");
const fs = require('fs')
const php = require("php2javascript");
const {
	FormData
} = require('sync-request');
const ExcelJS = require('exceljs');
const path = require('path');
const compressing = require('compressing');
// const workSheetsFromFile = xlsx.parse(`${__dirname}\\配置项.xlsx`);
// const config_sheet = workSheetsFromFile[0].data;
// const global_download_path = config_sheet[1][2];
const global_access_token = 'Basic c2FiZXI6c2FiZXJfc2VjcmV0';
const untils = new Untils(global_access_token);
// body > section > section > section > main > div.g-layout-main__content > div > div.hide-side-layout__container-content > div > div.hide-side-layout__page-content > div > div:nth-child(4) > div.t-col.t-col-10 > div > button.t-button.t-button--variant-base.t-button--theme-primary > span

const abc = pbottleRPA.browserCMD_text('div[class="button__export"] > button > span:nth-child(1)')
console.log(abc)
// console.log('global.traceId',global.traceId);
// console.log('进入gather_invoice')
// 		pbottleRPA.openURL(
// 			`https://dppt.jiangsu.chinatax.gov.cn:8443/invoice-query/invoice-query/`)

// 		var count = 0
// 		let chaxuntype = untils.waitImage("/input/1920/chaxuntype.png")
// 		if (chaxuntype) {
// 			pbottleRPA.moveMouseSmooth(chaxuntype.x, chaxuntype.y)
// 		}
// 		for (let index = 0; index < 2; index++) {
// 			if (index == 1) {
// 				//chaxuntype.png
// 				pbottleRPA.moveMouseSmooth(chaxuntype.x + 150, chaxuntype.y)
// 				pbottleRPA.mouseClick() //focus
// 				pbottleRPA.browserCMD_click(`li span:contains(取得发票)`)
// 			}

// 			const fplx = (index === 0) ? '销项' : '进项';
// 			// 获取当前日期
// 			var currentDate = new Date();

// 			// 获取当前日期的上一个月
// 			currentDate.setMonth(currentDate.getMonth() - 1);

// 			// 循环输出36个月的数据
// 			for (var i = 1; i <= 36; i++) {
// 				var currentCount = 0;
// 				if (index == 1 && count != currentCount) {
// 					//chaxuntype.png
// 					currentCount = count
// 					pbottleRPA.moveMouseSmooth(chaxuntype.x + 150, chaxuntype.y)
// 					pbottleRPA.mouseClick() //focus
// 					pbottleRPA.browserCMD_click(`li span:contains(取得发票)`)
// 				}
// 				// 获取1个月前的日期
// 				var startDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);

// 				// 获取当前月份的最后一天
// 				var endDate = new Date(currentDate.getFullYear(), currentDate.getMonth()+1 , 0);

// 				// 输出日期范围
// 				console.log(formatDate(startDate) + '---------->' + formatDate(endDate));

// 				// 将当前日期往前推12个月
// 				currentDate.setMonth(currentDate.getMonth() - 1);

// 				pbottleRPA.browserCMD_click(`input[placeholder="开票日期起"]`)
// 				pbottleRPA.browserCMD_val('input[placeholder="开票日期起"]', formatDate(startDate));
// 				pbottleRPA.keyTap('Enter')

// 				pbottleRPA.browserCMD_click(`input[placeholder="开票日期止"]`)
// 				pbottleRPA.browserCMD_val('input[placeholder="开票日期止"]', formatDate(endDate));
// 				pbottleRPA.keyTap('Enter')

// 				pbottleRPA.browserCMD_click(`button span:contains(查询)`)
// 					console.log("-------------查询结束---------------")

// 				for (var ins = 1; ins <= 10; ins++) {
// 					pbottleRPA.sleep(500)
// 					let text = pbottleRPA.browserCMD_text(
// 						`div[class="t-pagination__total"]`) //div[class = "statistics-info"] span:nth-child(1)
// 					let match = text.match(/\d+/);
// 					let number = match ? parseInt(match[0]) : 0;
// 					console.log("===============", ins, text, number)
// 					pbottleRPA.browserCMD_click(`button span:contains(导出 )`)
// 					pbottleRPA.browserCMD_click(`li span:contains(导出全部)`)
// 					let downloading = untils.existImage("/input/1920/dowloading.png")
// 					// let wxts = untils.waitImage("/input/1920/wxts.png")
// 					if (downloading) {
// 						console.log("-------------直接开始下载---------------")
// 							break; 
// 					} else if (!downloading) {
// 						let submissionTimes = [];
// 						count++;
// 						// 发票数量大于2500，异步下载
// 						console.log("-------------提交异步下载---------------")
// 						if (!downloading) {
// 							console.log("温馨提示")
// 							let wxts1 = untils.waitImage("/input/1920/wxts.png")
// 							pbottleRPA.sleep(500)
// 							pbottleRPA.moveMouseSmooth(wxts1.x + 20, wxts1.y + 180)
// 							pbottleRPA.mouseClick() //focus
// 						}

// 						pbottleRPA.sleep('30000');
// 						pbottleRPA.keyTap('alt + Left')

// 						break;
// 					} else {
// 						console.log("-------------查询未结束---------------")
// 					}

// 					if (ins == 10) {
// 						console.log("-------------查询无发票数据--------------")
// 						console.log(`${formatDate(startDate)}` + '---' + `${formatDate(endDate)}` + "年度无" +
// 							`${fplx}` +
// 							"数据")
// 					}

// 				}
// 				let down1 = untils.waitImageDisappear("/input/1920/dowloading.png")
// 				if (down1) {
// 					console.log("-------------已下载---------------")
// 					pbottleRPA.sleep(3000)
// 				}
// 			}
// 		}

// function formatDate(date) {
//     var year = date.getFullYear();
//     var month = padZero(date.getMonth() + 1);
//     var day = padZero(date.getDate());
//     return year + '-' + month + '-' + day;
// }

// function padZero(num) {
//     return num < 10 ? '0' + num : '' + num;
// }