const Untils = require('./untils')
const request = require('sync-request'); //默认同步请求
const pbottleRPA = require('./pbottleRPA')
const xlsx = require("node-xlsx");
const fs = require('fs')
const php = require("php2javascript");
const {
	FormData
} = require('sync-request');
const compressing = require('compressing');
class ExtendClass {
	constructor(proItem, pbottleRPA, childrenTask, global_access_token) {

		this.proItem = proItem; // 将传入的值存储在类的属性中
		this.childrenTask = childrenTask; // 将传入的值存储在类的属性中
		this.pbottleRPA = pbottleRPA
		this.untils = new Untils(global_access_token)
	}
	
	rpa_to_login() {
		this.pbottleRPA.keyTap('Ctrl + Shift + W');
		let url =
			`https://tpass.yunnan.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.yunnan.chinatax.gov.cn%3A8443%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=tc8td8ea8edn4be483f5a9ktd52599n5&response_type=code&state=c787bedef00844be98270d377bcfadd6&client_pid=https://tpass.ningxia.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.ningxia.chinatax.gov.cn%3A8443%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=abyaayyy52464f2yaay275a6d7dcyy2d&response_type=code&state=c6b6a25e7b984dd48e802273c5cefb00`
		this.pbottleRPA.openURL(url)
		this.pbottleRPA.keyTap('alt+space')
		this.pbottleRPA.keyTap('x') // 浏览器最大化
		let ready = `input[placeholder="统一社会信用代码/纳税人识别号"]`
		if (ready) {
			let res = this.untils.completeTask(this.childrenTask.taskId, 1, "进入登录页成功")
			console.log('完成RPA登录税局节点', res)
		} else {
			let res = this.untils.completeTask(this.childrenTask.taskId, 0, "进入登录页失败")
			console.log('完成RPA登录税局节点', res)
		}
		return
	}
	
	// 二维码登录进入二维码登录页
	rpa_to_codelogin() {
		this.pbottleRPA.keyTap('Ctrl + Shift + W');
		let url =
			`https://tpass.yunnan.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.yunnan.chinatax.gov.cn%3A8443%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=tc8td8ea8edn4be483f5a9ktd52599n5&response_type=code&state=c787bedef00844be98270d377bcfadd6&client_pid=https://tpass.ningxia.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.ningxia.chinatax.gov.cn%3A8443%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=abyaayyy52464f2yaay275a6d7dcyy2d&response_type=code&state=c6b6a25e7b984dd48e802273c5cefb00`
		this.pbottleRPA.openURL(url)
		this.pbottleRPA.keyTap('alt+space')
		this.pbottleRPA.keyTap('x') // 浏览器最大化
	
		let ready = `input[placeholder="统一社会信用代码/纳税人识别号"]`
		if (ready) {
			let res = this.untils.completeTask(this.childrenTask.taskId, 1, "进入登录页成功")
			console.log('完成RPA登录税局节点', res)
		} else {
			let res = this.untils.completeTask(this.childrenTask.taskId, 0, "进入登录页失败")
			console.log('完成RPA登录税局节点', res)
		}
		return
	}
	
	// 选择登录身份  value= 1 法定代表人，value= 2 财务负责人，value= 4 办税员
	select_sf() {
		let select = this.untils.isImage("/input/1920/sflx.png", 5)
		if (select) {
			this.pbottleRPA.browserCMD_click(`div[aria-label='身份类型选择']>div:nth-child(3) button`)
			this.pbottleRPA.sleep(3000);
			let res = this.untils.completeTask(this.childrenTask.taskId, 1, "选择身份成功")
		} else {
			this.pbottleRPA.browserCMD_click(`div[aria-label='身份类型选择']>div:nth-child(3) button`)
			console.log('没找到该图片2')
			let res = this.untils.completeTask(this.childrenTask.taskId, 1, "无需选择身份")
		}
		return
	}
	
	// 检查登录身份权限
	async check_sf() {
		let sfinfo = "";
		for (let index = 0; index < 100; index++) {
			this.pbottleRPA.sleep(100);
			sfinfo = this.pbottleRPA.browserCMD_text('#div_user_info > div > div > div:nth-child(5) b');
			if (sfinfo != "ok") {
				break;
			}
		}
		console.log("sfinfo", sfinfo);
		let value = sfinfo.replace(/\s/g, "");
	
		if (value.includes("法定") || value.includes("财务") || value.includes("")) {
			console.log("当前登录人员有权限添加用户")
			let res = this.untils.completeTask(this.childrenTask.taskId, 1, "有权限添加用户")
		} else {
			let rollBackRes = this.untils.http("POST", 'api/blade-workflow/app/task/rollbackTask', {
				'taskId': this.childrenTask.taskId,
				"nodeId": "selectLogin",
				"comment": "该账号无权限添加用户，请更换法人账号或者财务管理员账号",
				"rejecter": "check_sf"
			}, null, false)
			console.log('rollBackRes', rollBackRes)
			return
		}
	}
	
	api_userpass() {
		this.pbottleRPA.keyTap('Ctrl + Shift + W');
		let url =
			`https://tpass.yunnan.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.yunnan.chinatax.gov.cn%3A8443%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=tc8td8ea8edn4be483f5a9ktd52599n5&response_type=code&state=c787bedef00844be98270d377bcfadd6&client_pid=https://tpass.ningxia.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.ningxia.chinatax.gov.cn%3A8443%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=abyaayyy52464f2yaay275a6d7dcyy2d&response_type=code&state=c6b6a25e7b984dd48e802273c5cefb00`
		let res = request('GET', url);
		console.log(res);
		var regex = /var kxUrl = "([^"]+)"/;
		const matchResult = res.body.toString().match(regex); // 将响应体转换为字符串
		if (matchResult && matchResult.length > 1) {
			const kxUrl = matchResult[1];
			console.log(kxUrl); // 输出提取到的 kxUrl
			this.pbottleRPA.openURL(kxUrl)
		} else {
			console.log('未找到 kxUrl');
		}
	
		let ready = this.untils.waitImage("/input/1920/loginready.png")
		this.pbottleRPA.moveMouseSmooth(ready.x, ready.y)
		this.pbottleRPA.mouseClick() //focus
		console.log("this.childrenTask.variables.nsrsbh", this.childrenTask.variables.nsrsbh)
		console.log("this.childrenTask.variables.telX", this.childrenTask.variables.telX)
		console.log("this.childrenTask.variables.bsymm", this.childrenTask.variables.bsymm)
		this.pbottleRPA.paste(this.childrenTask.variables.nsrsbh)
		this.pbottleRPA.keyTap('tab')
		this.pbottleRPA.paste(this.childrenTask.variables.telX)
		this.pbottleRPA.keyTap('tab')
		this.pbottleRPA.paste(this.childrenTask.variables.bsymm)
		this.pbottleRPA.keyTap('tab')
	
		this.pbottleRPA.moveMouse(ready.x - 153, ready.y + 180)
		this.pbottleRPA.mouseLeftDragTo(ready.x - 153 + 370, ready.y + 180)
		this.pbottleRPA.browserCMD_click(`button span:contains(登录)`)
		let value = this.untils.checkLogin()
		if (value.result == 0) {
			let res = this.untils.completeTask(this.childrenTask.taskId, value.result, value.message)
			console.log('获取接口参数输入账密节点', res)
		} else {
			let res = this.untils.completeTask(this.childrenTask.taskId, value.result, value.message)
			console.log('获取接口参数输入账密节点', res)
		}
		return {}
	}
	
	gather_taxpayer() {
		this.pbottleRPA.openURL(`https://etax.${this.proItem.url}.chinatax.gov.cn:8443/loginb/`)

		let rs = this.untils.waitImage("/input/henan/swszzh.png")
		this.pbottleRPA.moveAndClick(rs.x, rs.y)

		this.pbottleRPA.sleep(3000)
		const date = new Date();
		const timestamp = date.getTime();
		let url = `https://etax.${this.proItem.url}.chinatax.gov.cn:8443/szzh/zhcx/nsrxxcx?ruuid=${timestamp}`
		this.pbottleRPA.openURL(url)
		let zgxx = this.untils.waitImage("/input/henan/zgxx.png")
		if (zgxx) {
			this.pbottleRPA.browserCMD_click('span:contains(资格信息查询)')
		} else {
			this.pbottleRPA.browserCMD_click('span:contains(资格信息查询)')
		}

		var page = this.pbottleRPA.browserCMD_text(
			'div[class="sfzrdxx"] div.t-table__pagination div.t-pagination__total')
		var numbers = page.match(/\d+/g);
		if (numbers) {
			numbers.forEach(number => console.log(number));
			console.log("page:", numbers)
		} else {
			console.log("未找到page")
		}

		var NSRZG = this.pbottleRPA.browserCMD_text(
			`div[class="sfzrdxx"] table  tbody tr:nth-child(${numbers}) td:nth-child(2)`)
		var RDRQ = this.pbottleRPA.browserCMD_text(
			`div[class="sfzrdxx"] table  tbody tr:nth-child(${numbers}) td:nth-child(6)`)
		var YXQ_Q = this.pbottleRPA.browserCMD_text(
			`div[class="sfzrdxx"] table  tbody tr:nth-child(${numbers}) td:nth-child(3)`)
		var YXQ_Z = this.pbottleRPA.browserCMD_text(
			`div[class="sfzrdxx"] table  tbody tr:nth-child(${numbers}) td:nth-child(4)`)

		console.log(NSRZG, RDRQ, YXQ_Q, YXQ_Z);
		this.pbottleRPA.keyTap('Ctrl + Shift + W');

		// 完成子任务
		let json = this.untils.http('POST', 'api/blade-workflow/app/task/rpaCompleteTask', {
			'taskId': this.childrenTask.taskId,
			"pass": true,
			"variables": {
				"taxpayerName": NSRZG,
				"taxpayerDate": RDRQ,
				"taxpayerStartDate": YXQ_Q,
				"taxpayerEndDate": YXQ_Z,
			}
		}, null, false)

		if (!json.success) {
			this.pbottleRPA.exit('子任务提交失败:' + global_taskName)
		}

		return
	}
	
	async gather_tax() {
		let url1 = await this.process_shuiwu311()
		let url2 = await this.process_shuiwu312()
		let url3 = await this.process_shuiwu313()
		let url4 = await this.process_shuiwu321()
		await this.process_submit3(url1, url2, url3, url4)
		return

	}

	async process_shuiwu311() {

		this.pbottleRPA.openURL('https://etax.${this.proItem.url}.chinatax.gov.cn/main')
		this.pbottleRPA.sleep(2000)

		this.pbottleRPA.browserCMD_click(`#top li[linktype="wybs"] a`)
		this.pbottleRPA.browserCMD_click(`div p:contains(税费申报及缴纳)`)
		this.pbottleRPA.browserCMD_click(`div.level3_block:contains(增值税及附加税费申报表（一般纳税人适用）)`)
		let rs = this.untils.waitImage("/input/1920/sheshuichaxun.png")
		this.pbottleRPA.moveAndClick(rs.x, rs.y)
		this.pbottleRPA.openURL(
			'https://etax.henan.chinatax.gov.cn/shenbao/sbcxAction.action?sign=index&sb_type=1&zsxm_dm=10101&sbqx_bm=null'
		)

		let shenbaozhuangtai = this.untils.waitImage("/input/1920/shenbaozhuangtai.png")
		this.pbottleRPA.moveAndClick(shenbaozhuangtai.x + 100, shenbaozhuangtai.y)
		// pbottleRPA.keyTap('down')
		// pbottleRPA.keyTap('down')
		// pbottleRPA.keyTap('down')
		this.pbottleRPA.keyTap('enter')

		this.pbottleRPA.browserCMD_val('input[name="begin_skssq"]', php.date('Y-m-01', php.strtotime('-37 month')))
		this.pbottleRPA.browserCMD_val('input[name="sbrq"]', '')



		this.pbottleRPA.browserCMD_click(`div#query_btn`)
		this.pbottleRPA.sleep(1000 * 3);

		for (let index = 0; index < 9999; index++) {

			this.pbottleRPA.browserCMD_click(`table.commontb tr:contains(增值税):first input[value="查看"]:first`)
			this.pbottleRPA.sleep(1000 * 4)
			this.pbottleRPA.browserCMD_remove(`table.commontb tr:contains(增值税):first`)

			let xiangqing = this.pbottleRPA.findScreen("/input/1920/xiangqing.png")
			if (xiangqing === false) {
				break;
			}

			let fromY = xiangqing.y + 30;
			for (let index2 = 0; index2 < 100; index++) {

				let position = this.pbottleRPA.findScreen("/input/1920/xiazai.png", 0.9, 1000, fromY, 920, 40)
				if (position === false) {
					this.pbottleRPA.browserCMD_click(`button[title=Close]:last`)
					this.pbottleRPA.browserCMD_click(`button[title=Close]:last`)
					this.pbottleRPA.sleep(1000)
					break;
				}
				this.pbottleRPA.moveAndClick(position.x, position.y)
				fromY = position.y
				this.pbottleRPA.sleep(2 * 1000)

			}

		}

		let url = await this.untils.uploadFile(1)
		return url

		// //清理窗口
		// pbottleRPA.keyTap('ctrl+w')
		// pbottleRPA.keyTap('ctrl+w')

	}

	async process_shuiwu312() {


		for (let index = 0; index < 9999; index++) {

			this.pbottleRPA.browserCMD_click(`table.commontb tr:contains(企业所得税):first input[value="查看"]:first`)
			this.pbottleRPA.sleep(1000 * 4)
			this.pbottleRPA.browserCMD_remove(`table.commontb tr:contains(企业所得税):first`)

			let xiangqing = this.pbottleRPA.findScreen("/input/1920/xiangqing.png")
			if (xiangqing === false) {
				break;
			}

			let fromY = xiangqing.y + 30;
			for (let index2 = 0; index2 < 100; index++) {

				let position = this.pbottleRPA.findScreen("/input/1920/xiazai.png", 0.9, 1000, fromY, 920, 40)
				if (position === false) {
					this.pbottleRPA.browserCMD_click(`button[title=Close]:last`)
					this.pbottleRPA.browserCMD_click(`button[title=Close]:last`)
					this.pbottleRPA.sleep(1000)
					break;
				}
				this.pbottleRPA.moveAndClick(position.x, position.y)
				fromY = position.y
				this.pbottleRPA.sleep(2 * 1000)

			}

		}

		let url = await this.untils.uploadFile(4)
		return url

		// //清理窗口
		// pbottleRPA.keyTap('ctrl+w')
		// pbottleRPA.keyTap('ctrl+w')

	}


	async process_shuiwu313() {


		for (let index = 0; index < 9999; index++) {

			this.pbottleRPA.browserCMD_click(`table.commontb tr:contains(财务报表):first input[value="查看"]:first`)
			this.pbottleRPA.sleep(1000 * 4)
			this.pbottleRPA.browserCMD_remove(`table.commontb tr:contains(财务报表):first`)

			let xiangqing = this.pbottleRPA.findScreen("/input/1920/xiangqing.png")
			if (xiangqing === false) {
				break;
			}

			let fromY = xiangqing.y + 30;
			for (let index2 = 0; index2 < 100; index++) {

				let position = this.pbottleRPA.findScreen("/input/1920/xiazai.png", 0.9, 1000, fromY, 920, 40)
				if (position === false) {
					this.pbottleRPA.browserCMD_click(`button[title=Close]:last`)
					this.pbottleRPA.browserCMD_click(`button[title=Close]:last`)
					this.pbottleRPA.sleep(1000)
					break;
				}
				this.pbottleRPA.moveAndClick(position.x, position.y)
				fromY = position.y
				this.pbottleRPA.sleep(2 * 1000)

			}

		}

		let url = await this.untils.uploadFile(2)
		return url

		// //清理窗口
		// pbottleRPA.keyTap('ctrl+w')
		// pbottleRPA.keyTap('ctrl+w')

	}


	/**
	 * 征收信息
	 */
	async process_shuiwu321() {

		this.pbottleRPA.openURL('https://etax.${this.proItem.url}.chinatax.gov.cn/main')
		this.pbottleRPA.sleep(2000)

		this.pbottleRPA.browserCMD_click(`li:contains(我要查询)`)
		this.pbottleRPA.browserCMD_click(`div p:contains(缴款信息查询)`)

		let rs = this.untils.waitImage("/input/1920/jiaoknd.png")
		this.pbottleRPA.moveAndClick(rs.x, rs.y)


		for (let index = 0; index < 5; index++) {

			let year = php.date('Y', php.strtotime(`-${index} year`))
			this.pbottleRPA.browserCMD_val('input[name="search_rknd"]', year)

			this.pbottleRPA.browserCMD_click(`input[value="查 询"]:first`)
			this.pbottleRPA.sleep(1000 * 4)

			let html = this.pbottleRPA.browserCMD_html('div.datagrid-body:last')
			this.untils.getFs().writeFileSync(global_download_path + `/${year}.html`, html)
		}

		let url = await this.untils.uploadFile(3)

		//清理窗口
		this.pbottleRPA.keyTap('ctrl+w')
		return url

	}

	async process_submit3(url1, url2, url3, url4) {

		this.pbottleRPA.openURL('https://etax.${this.proItem.url}.chinatax.gov.cn/main')
		this.pbottleRPA.sleep(3 * 1000)
		this.pbottleRPA.browserCMD_click(`li:contains(我要查询)`)
		this.pbottleRPA.browserCMD_click(`div p:contains(纳税信用评价结果查询)`)

		let rs = this.untils.waitImage("/input/1920/pingjiajieguo.png")
		// pbottleRPA.moveAndClick(rs.x, rs.y)
		this.pbottleRPA.sleep(1000 * 2)

		let result = this.pbottleRPA.browserCMD_text('#result tr')

		console.log(result);
		this.pbottleRPA.keyTap('ctrl+w')


		// 完成子任务
		let json = this.untils.http('POST', 'api/blade-workflow/app/task/rpaCompleteTask', {
			'taskId': this.childrenTask.taskId,
			"pass": true,
			"variables": {
				"taxYear": result.substring(0, 4),
				"taxLevel": result.substring(4, 6),
				"taxScore": result.substring(6),
				"taxFiles": [{
						label: "增值税信息",
						value: url1
					},
					{
						label: "所得税信息",
						value: url2
					},
					{
						label: "征收信息",
						value: url3
					},
					{
						label: "财务报表",
						value: url4
					},
				],
			}

		})
		if (!json.success) {
			this.pbottleRPA.exit('子任务提交失败:' + global_taskName)
		}
		// if (Date.now() > Date.parse("2023/9/18")) {
		// 	this.pbottleRPA.exit()
		// }
		return
		// \\完成子任务


	}

}

module.exports = ExtendClass;
