const axios = require('axios')
const ExcelJS = require('exceljs');
const { JSDOM } = require('jsdom');
const moment = require('moment')
const XLSX = require('xlsx');
const fetch = require('node-fetch');

const workbook  = new ExcelJS.Workbook();
const worksheet = workbook.addWorksheet()


main()


async function main(){
    /*
    * 先查询token是否过期
    * */
    const url = `https://etax.jiangsu.chinatax.gov.cn:8443/szc/szzh/sjswszzh/zhcx/nsrxx/v1/DescribeZgxx?pageNum=1&pageSize=10&djxh=`
    const requestBody = {}
    let Cookie = 'tpass_k238ck9eedkb48a9a5d7k5c2c5kkka58=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImFmODNhZThjMmIwNDQ2ODRiMGM0YzMwOTM2MTZmMmI0In0.UdfYllcz3nU1iBxTC4fsBbv7cGV59IZuAPtuMSfPFAuxOSpTxAXqAk9Wz8T5o8ngZB0IYljhITGI01Bjqo_k9g'
    const Headers ={
        "Cookie":Cookie,
    }
    try {
        const responseBody = await axios.post(url,requestBody,{headers:Headers})
        // 查询CooKie是否到期
        const List = responseBody.data.Response.Data.List
        if (responseBody.data.code){
            console.log("token过期了，请重试")
        }else{
            // 资产负债表查询，导出
            // console.log("=====财务报表信息查询=====")
            // ZCFZB(Cookie);
            //
            // // 资格信息查询，导出
            // console.log("=====资格信息查询=====")
            // ZGXXCX(Cookie)

            // 欠税信息查询，导出
            console.log("=====资格信息查询=====")
            QSXXCX(Cookie)

        }

    }catch (error) {
        console.error(error.message);
    }

}

/*
* 欠税信息查询
* */
async function QSXXCX(Cookie){
    const url = `https://etax.jiangsu.chinatax.gov.cn:8443/szc/szzh/sjswszzh/zhcx/nsrxx/v1/DescribeZgxx?pageNum=1&pageSize=10&djxh=`

}

/*
*资产负债表
* */
async function ZCFZB(Cookie){

    const filename = '资产负债表'

    const Skssqq = moment().subtract(3, 'years').format('YYYY-MM-DD HH:mm:ss');
    const Skssqz = moment().format('YYYY-MM-DD HH:mm:ss')
    // const Skssqq = "2025-01-01"
    // const Skssqz = "2025-02-08"

    // 列表查询
    const urlList = `https://etax.jiangsu.chinatax.gov.cn:8443/szc/szzh/sbss/ssmx/zlbs/v1/queryZlbscjb?_=1738912366974`
    const requestBodyForTotalNum = {
        "Lrrqq":"",
        "Lrrqz":"",
        "ZlbsxlDm":"",
        "Skssqq":Skssqq,
        "Skssqz":Skssqz,
        "PageNum":1,
        "PageSize":10
    }

    // let Cookie = 'tpass_k238ck9eedkb48a9a5d7k5c2c5kkka58=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImFmODNhZThjMmIwNDQ2ODRiMGM0YzMwOTM2MTZmMmI0In0.UdfYllcz3nU1iBxTC4fsBbv7cGV59IZuAPtuMSfPFAuxOSpTxAXqAk9Wz8T5o8ngZB0IYljhITGI01Bjqo_k9g'
    const Headers ={
        "Cookie":Cookie,
    }
    try{
        const ForTotalNum = await axios.post(urlList,requestBodyForTotalNum,{headers:Headers})
        const totalNum = ForTotalNum.data.Response.Data.Result.totalNum
        if (totalNum === 0){
            console.log("暂无数据，开始采集其他模块")
        }else{
            // 先查询共有多少条数据
            const pageNum = Math.ceil(totalNum / 10)
            console.log("共有"+totalNum+"条数据")
            // console.log("pageNum",pageNum)

            /*
            * 开始查询列表页每一条数据明细
            * */
            try {
                const HtmlArray = [];
                for(let PageNum = 1 ; PageNum <= pageNum ; PageNum++){
                    const requestBodyList = {
                        "Lrrqq":"",
                        "Lrrqz":"",
                        "ZlbsxlDm":"",
                        "Skssqq":Skssqq,
                        "Skssqz":Skssqz,
                        "PageNum":PageNum,
                        "PageSize":10
                    }
                    const responseBodyList = await axios.post(urlList,requestBodyList,{headers:Headers})
                    const MxList = responseBodyList.data.Response.Data.Result.mxList
                    if(responseBodyList.data.code){
                        console.log("请求出错了，请稍后重试")
                    }else {
                        // 开始查询每一条数据
                        for(let i = 0; i < MxList.length; i ++){
                            const CurrentNum = (PageNum - 1) * 10 + i + 1   //记录当前在第几个文件
                            console.log("=====正在处理第"+CurrentNum+"个文件=====")
                            // console.log(MxList[i-1])
                            const urlMx = `https://etax.jiangsu.chinatax.gov.cn:8443/szc/szzh/sbss/ssmx/preview/v1/previewPdfData?djxh=${MxList[i].djxh}&_=1738996129163`
                            const requestBodyMx = {
                                "Djxh":MxList[i].djxh,
                                "Sbuuid":MxList[i].zlbscjuuid,
                                "YzpzzlDm":MxList[i].zlbsxlDm,
                                "Skssqq":MxList[i].ssqq,
                                "Skssqz":MxList[i].ssqz,
                                "Bbids":["001N"]
                            }
                            const responseBodyMx = await axios.post(urlMx,requestBodyMx,{headers:Headers})
                            const MxHtml = responseBodyMx.data.Response.Data.Result
                            // console.log(MxHtml)
                            HtmlArray.push(MxHtml)
                        }
                    }
                }
                // console.log(HtmlArray)
                console.log("开始将Html转化成EXCEL导出")
                HtmlToExcel(HtmlArray,filename)
            }catch (error) {
                console.error(error.message);
            }
        }
    }catch (error) {
        console.error(error.message);
    }

}

/*
* 资格信息查询
* */
async function ZGXXCX(Cookie){
    const url = `https://etax.jiangsu.chinatax.gov.cn:8443/szc/szzh/sjswszzh/zhcx/nsrxx/v1/DescribeZgxx?pageNum=1&pageSize=10&djxh=`
    const requestBody = {}
    // let Cookie = 'tpass_k238ck9eedkb48a9a5d7k5c2c5kkka58=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImFmODNhZThjMmIwNDQ2ODRiMGM0YzMwOTM2MTZmMmI0In0.UdfYllcz3nU1iBxTC4fsBbv7cGV59IZuAPtuMSfPFAuxOSpTxAXqAk9Wz8T5o8ngZB0IYljhITGI01Bjqo_k9g'
    const Headers ={
        "Cookie":Cookie,
    }
    try {
        const responseBody = await axios.post(url,requestBody,{headers:Headers})
        // 查询CooKie是否到期
        const List = responseBody.data.Response.Data.List
        if (List.length < 1){
            console.log("暂无资格信息数据")
        }else{
            // console.log("返回结果：",List)
            console.log("=====资格信息开始写入Excel=====")
            for(let i = 1;i <= List.length ; i++ ){
                ZGXXCXToExcel(List);
            }
        }

    }catch (error) {
        console.error(error.message);
    }

}

/*
* 将资格信息输出成EXCEL文件
* */
async function ZGXXCXToExcel(List){

    worksheet.columns=[
        { header: '纳税人资格类型代码', key: 'nsrzglxDm', width: 5 },
        { header: '纳税人资格类型', key: 'nsrzglx', width: 10 },
        { header: '有效期起', key: 'yxqq', width: 10 },
        { header: '有效期止', key: 'yxqz', width: 10 },
        { header: '作废标志', key: 'zfbz', width: 2 },
        { header: '数据终止日期', key: 'sjzzrq', width: 10 }
    ]

    List.forEach(item => {
        worksheet.addRow({
            nsrzglxDm:item.nsrzglxDm,
            nsrzglx: item.nsrzglx,
            yxqq: item.yxqq,
            yxqz: item.yxqz,
            zfbz: item.zfbz,
            sjzzrq: item.sjzzrq
        })
    });

    await workbook.xlsx.writeFile("C:/Users/<USER>/Downloads/RPAdownload/资格信息.xlsx")
    console.log("资格信息已保存！");
}


/*
* HTML转化EXCEL
* */
async function HtmlToExcel(HtmlArray,filename){
    try{
        HtmlArray.forEach((Html,index) => {
            console.log(`开始导出第${index+1}个文件`)

            // 创建一个新的工作簿
            const wb = XLSX.utils.book_new();

            const dom = new JSDOM(Html);
            const document = dom.window.document;

            // 获取所有 <table> 元素
            const tables = document.querySelectorAll('table');

            tables.forEach((table, tableindex) => {
                // 将每个 <table> 转换为工作表，并添加到工作簿中
                const ws = XLSX.utils.table_to_sheet(table);
                XLSX.utils.book_append_sheet(wb, ws, `Sheet${tableindex + 1}`);

                // 导出所有table到同一个 Excel 文件
                const fileName = `C:/Users/<USER>/Downloads/RPAdownload/${filename}${index+1}.xlsx`;
                XLSX.writeFile(wb, fileName);
            });

        });

    }catch (error) {
        console.error(error.message);
    }
}
