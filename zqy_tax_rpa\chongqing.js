const Untils = require('./untils')
const request = require('sync-request'); //默认同步请求
const pbottleRPA = require('./pbottleRPA')
const xlsx = require("node-xlsx");
const fs = require('fs')
const php = require("php2javascript");
const {
	FormData
} = require('sync-request');
const compressing = require('compressing');
class ExtendClass {
	constructor(proItem, pbottleRPA, childrenTask, global_access_token) {

		this.proItem = proItem; // 将传入的值存储在类的属性中
		this.childrenTask = childrenTask; // 将传入的值存储在类的属性中
		this.pbottleRPA = pbottleRPA
		this.untils = new Untils()
	}
	
	rpa_to_login() {
		this.pbottleRPA.keyTap('Ctrl + Shift + W');
		let url =
			'https://tpass.chongqing.chinatax.gov.cn:8443/#/login?redirect_uri=https%3A%2F%2Fetax.chongqing.chinatax.gov.cn%3A8443%2Fmhzx%2Fapi%2Fmh%2Ftpass%2Fcode&client_id=y75ey676f5e445f6bs4f5b78d45bs2b6&response_type=code&state=fcf49959cfd243baa7f6f9a7d19534a2&client_pid=y75ey676f5e445f6bs4f5b78d45bs2b6'
		this.pbottleRPA.openURL(url)
		this.pbottleRPA.sleep(500)
		this.untils.closeOtherPages()
		this.pbottleRPA.keyTap('alt+space')
		this.pbottleRPA.keyTap('x') // 浏览器最大化

		let ready = `input[placeholder="统一社会信用代码/纳税人识别号"]`
		if (ready) {
			console.log('完成RPA打开税局页面节点')
		}else {
			this.untils.terminateTask(
				this.childrenTask.flowId,
				2,
				this.childrenTask.variable,
				'进入登录页失败'
			)
			console.log('进入登录页失败')
            this.pbottleRPA.exit('进入登录页失败')

		}
		return
	}

	
	// 选择登录身份
	select_sf() {
		// this.pbottleRPA.browserCMD_click(`div[role="radiogroup"] span input[value="01"]`)
		// this.pbottleRPA.sleep(500)
		// this.pbottleRPA.browserCMD_click(`div[aria-label='身份类型选择']>div:nth-child(3) button`)
		let select = this.untils.isImage("/input/1920/sflx.png", 5)
		if (select) {
			this.pbottleRPA.browserCMD_click(`div[aria-label='身份类型选择']>div:nth-child(3) button`)
			this.pbottleRPA.sleep(3000)
			let res = this.untils.completeTask(
				this.childrenTask.id, 
				this.childrenTask.flowId, 
				this.childrenTask.flowKey, 
				this.childrenTask.taskKey,
				this.childrenTask.variable, 
				1, 
				"选择身份成功"
			)
		} else {
			this.pbottleRPA.browserCMD_click(`div[aria-label='身份类型选择']>div:nth-child(3) button`)
			this.pbottleRPA.sleep(3000)
			console.log('没找到该图片2')
			let res = this.untils.completeTask(
				this.childrenTask.id, 
				this.childrenTask.flowId, 
				this.childrenTask.flowKey, 
				this.childrenTask.taskKey, 
				this.childrenTask.variable, 
				1, 
				"无需选择身份"
			)
		}
		return 1
	}
	
}

module.exports = ExtendClass;
