const axios = require('axios')
const ExcelJS = require('exceljs');
const xml2js = require('xml2js')
const moment = require('moment')
const async = require('async')
const CryptoJS = require('crypto-js')
const pbottleRPA = require('./pbottleRPA')




/*
* 定时任务
* */
console.log("-------定时任务启动---------")
let intervalId = setInterval(async function () {
    console.log("当前时间为：" + moment().format('YYYY-MM-DD HH:mm:ss'))
    // console.log("=====申报信息查询=====")
    // await SBXXCX();
    console.log("=====财务报表信息查询=====")
    await CWBBXXCX();
    // console.log("=====缴款信息查询=====")
    // await JKXXCX();
    // console.log("=====纳税人信息查询=====")
    // await NSRXXCX();
}, 1000 );

// ZGXXCX();

/*
* 申报信息查询
* */
async function SBXXCX(){
    /*
    *加密
    * */
    const requestBody = {
        "gzlx":["1","5"],
        "pageNum":1,
        "pageSize":10,
        "sbrqq":"2025-01-01",
        "sbrqz":"2025-01-31",
        "skssqq":"",
        "skssqz":"",
        "zfbz":"N",
        "yzpzzlDm":""
    }

    /*
    * 执行参数
    * */
    const url = "https://etax.jiangsu.chinatax.gov.cn:8443/szc/szzh/sjswszzh/zhcx/v1/DescribeSbmxcx2"
    let Cookie = "tpass_k238ck9eedkb48a9a5d7k5c2c5kkka58=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjhhOWExNTk2OWVkZjRmZDFhMTAwMjlhMjdkMGI2OWUwIn0.D-eX5c7YcNY8WW2VqLMSI5RJboIiHhxoy2lQub8SbninvtCmkgH-JwTUamMmDwMlfs6tjTEo691h5pRy5h5K_w"
        // "sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%2219273fee980582-0dcbe78f10a5308-26001b51-2073600-19273fee981b2c%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTkyNzNmZWU5ODA1ODItMGRjYmU3OGYxMGE1MzA4LTI2MDAxYjUxLTIwNzM2MDAtMTkyNzNmZWU5ODFiMmMifQ%3D%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A%22%22%7D%2C%22%24device_id%22%3A%2219273fee980582-0dcbe78f10a5308-26001b51-2073600-19273fee981b2c%22%7D; Hm_lvt_cdef0adc5294fae9be3436c5a50f805f=1736245772; SF_cookie_43=16290870; oauth2_referer=etax.jiangsu.chinatax.gov.cn; tpass_tct8zta97w6c46zdt9zc2648227df5z2=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImZjNzU1NWQwYWVjZDRhMmY4ODM4MzBkYzM5MzE1OTZmIn0.fun5uNzZOkOnQv1Rcxe58zaGPljpa1e5ITh66iBjKLaoSEo2fhr07cxcmBMR2oaXsJpkworwiqwuNdFKgtGQ7w; refer=etax.zhejiang.chinatax.gov.cn; dzfp-ssotoken=45dc467d27f442e5b4cd801cef695e62; SSO_SECURITY_CHECK_TOKEN=18bce01ed0ff44a28c541a5982975bb0; SSOSESSIONID=mbreChAcRK6Q1XVFP_28k9Yaas49T7Fvnb1YDTl36ihHwjmw-8xd!-16901149; tpass_mcsc7e2ssscb4sfmbsmas35sass2753b=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImI5N2NhNTFiMmFkODRlNGFiZjBiYmJmZWJmNzRhZWU0In0.cwTLnM2Iu3l2RxayfWrpMHdzr5uegJSSFrrcApjKjusZFmtTF1Kq4httmlCwjBy2DgBN0OsgTSZ_oInzsJ3hNQ; znhd-ssotoken=1d8946f3d5dc4c719b00f36dc315274c; ZNHD_SECURITY_CHECK_TOKEN=a3e8f029c835438c8bb83dd149d2c963; tpass_k238ck9eedkb48a9a5d7k5c2c5kkka58=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijk2OTIxNDlhYTM0NTRmYTU4MjNjYTI3YTY5N2M4OWI5In0.R5gvVfS7TSDAjv8Xesi7Mi1svyE8dK-jDNiDCledU-umtW_facMuiKL1PL0sb3CNXPoXL-A6ozlfz0zZZmbHtQ; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%2219273fee980582-0dcbe78f10a5308-26001b51-2073600-19273fee981b2c%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTkyNzNmZWU5ODA1ODItMGRjYmU3OGYxMGE1MzA4LTI2MDAxYjUxLTIwNzM2MDAtMTkyNzNmZWU5ODFiMmMifQ%3D%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A%22%22%7D%2C%22%24device_id%22%3A%2219273fee980582-0dcbe78f10a5308-26001b51-2073600-19273fee981b2c%22%7D; Hm_lvt_cdef0adc5294fae9be3436c5a50f805f=1736245772; SF_cookie_43=16290870; tpass_tct8zta97w6c46zdt9zc2648227df5z2=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImZjNzU1NWQwYWVjZDRhMmY4ODM4MzBkYzM5MzE1OTZmIn0.fun5uNzZOkOnQv1Rcxe58zaGPljpa1e5ITh66iBjKLaoSEo2fhr07cxcmBMR2oaXsJpkworwiqwuNdFKgtGQ7w; refer=etax.zhejiang.chinatax.gov.cn; SSOSESSIONID=mbreChAcRK6Q1XVFP_28k9Yaas49T7Fvnb1YDTl36ihHwjmw-8xd!-16901149; tpass_mcsc7e2ssscb4sfmbsmas35sass2753b=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImI5N2NhNTFiMmFkODRlNGFiZjBiYmJmZWJmNzRhZWU0In0.cwTLnM2Iu3l2RxayfWrpMHdzr5uegJSSFrrcApjKjusZFmtTF1Kq4httmlCwjBy2DgBN0OsgTSZ_oInzsJ3hNQ; oauth2_referer=etax.jiangsu.chinatax.gov.cn; znhd-ssotoken=1d8946f3d5dc4c719b00f36dc315274c; ZNHD_SECURITY_CHECK_TOKEN=a3e8f029c835438c8bb83dd149d2c963; tpass_k238ck9eedkb48a9a5d7k5c2c5kkka58=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijk2OTIxNDlhYTM0NTRmYTU4MjNjYTI3YTY5N2M4OWI5In0.R5gvVfS7TSDAjv8Xesi7Mi1svyE8dK-jDNiDCledU-umtW_facMuiKL1PL0sb3CNXPoXL-A6ozlfz0zZZmbHtQ"
        const Headers ={
        "Cookie":Cookie,
    }

    try {
        const responseBody = await axios.post(url,requestBody,{headers:Headers})
        if (responseBody.data.code) {
            console.log("异常原因为：" + responseBody.data.msg + "定时器取消");
            clearInterval(intervalId); // 取消定时器
        }else{
            console.log("返回结果:",responseBody.data.Response.Data)
        }
    }catch (error){
        console.log("请求出错"+error)
    }

}

/*
* 财务报表信息查询
* */
async function CWBBXXCX(){
    /*
    *加密
    * */
    const requestBody = {
        "Lrrqq":"2025-02-01",
        "Lrrqz":"2025-02-06",
        "ZlbsxlDm":"",
        "Skssqq":"",
        "Skssqz":"",
        "PageNum":1,
        "PageSize":10
    }

    /*
    * 执行参数
    * */
    const timestamp = Date.now();
    const url = `https://etax.jiangsu.chinatax.gov.cn:8443/szc/szzh/sbss/ssmx/zlbs/v1/queryZlbscjb?_=${timestamp}`
    // "sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%2219273fee980582-0dcbe78f10a5308-26001b51-2073600-19273fee981b2c%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTkyNzNmZWU5ODA1ODItMGRjYmU3OGYxMGE1MzA4LTI2MDAxYjUxLTIwNzM2MDAtMTkyNzNmZWU5ODFiMmMifQ%3D%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A%22%22%7D%2C%22%24device_id%22%3A%2219273fee980582-0dcbe78f10a5308-26001b51-2073600-19273fee981b2c%22%7D; Hm_lvt_cdef0adc5294fae9be3436c5a50f805f=1736245772; SF_cookie_43=16290870; oauth2_referer=etax.jiangsu.chinatax.gov.cn; tpass_tct8zta97w6c46zdt9zc2648227df5z2=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImZjNzU1NWQwYWVjZDRhMmY4ODM4MzBkYzM5MzE1OTZmIn0.fun5uNzZOkOnQv1Rcxe58zaGPljpa1e5ITh66iBjKLaoSEo2fhr07cxcmBMR2oaXsJpkworwiqwuNdFKgtGQ7w; refer=etax.zhejiang.chinatax.gov.cn; dzfp-ssotoken=45dc467d27f442e5b4cd801cef695e62; SSO_SECURITY_CHECK_TOKEN=18bce01ed0ff44a28c541a5982975bb0; SSOSESSIONID=mbreChAcRK6Q1XVFP_28k9Yaas49T7Fvnb1YDTl36ihHwjmw-8xd!-16901149; tpass_mcsc7e2ssscb4sfmbsmas35sass2753b=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImI5N2NhNTFiMmFkODRlNGFiZjBiYmJmZWJmNzRhZWU0In0.cwTLnM2Iu3l2RxayfWrpMHdzr5uegJSSFrrcApjKjusZFmtTF1Kq4httmlCwjBy2DgBN0OsgTSZ_oInzsJ3hNQ; znhd-ssotoken=1d8946f3d5dc4c719b00f36dc315274c; ZNHD_SECURITY_CHECK_TOKEN=a3e8f029c835438c8bb83dd149d2c963; tpass_k238ck9eedkb48a9a5d7k5c2c5kkka58=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijk2OTIxNDlhYTM0NTRmYTU4MjNjYTI3YTY5N2M4OWI5In0.R5gvVfS7TSDAjv8Xesi7Mi1svyE8dK-jDNiDCledU-umtW_facMuiKL1PL0sb3CNXPoXL-A6ozlfz0zZZmbHtQ; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%2219273fee980582-0dcbe78f10a5308-26001b51-2073600-19273fee981b2c%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTkyNzNmZWU5ODA1ODItMGRjYmU3OGYxMGE1MzA4LTI2MDAxYjUxLTIwNzM2MDAtMTkyNzNmZWU5ODFiMmMifQ%3D%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A%22%22%7D%2C%22%24device_id%22%3A%2219273fee980582-0dcbe78f10a5308-26001b51-2073600-19273fee981b2c%22%7D; Hm_lvt_cdef0adc5294fae9be3436c5a50f805f=1736245772; SF_cookie_43=16290870; tpass_tct8zta97w6c46zdt9zc2648227df5z2=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImZjNzU1NWQwYWVjZDRhMmY4ODM4MzBkYzM5MzE1OTZmIn0.fun5uNzZOkOnQv1Rcxe58zaGPljpa1e5ITh66iBjKLaoSEo2fhr07cxcmBMR2oaXsJpkworwiqwuNdFKgtGQ7w; refer=etax.zhejiang.chinatax.gov.cn; SSOSESSIONID=mbreChAcRK6Q1XVFP_28k9Yaas49T7Fvnb1YDTl36ihHwjmw-8xd!-16901149; tpass_mcsc7e2ssscb4sfmbsmas35sass2753b=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImI5N2NhNTFiMmFkODRlNGFiZjBiYmJmZWJmNzRhZWU0In0.cwTLnM2Iu3l2RxayfWrpMHdzr5uegJSSFrrcApjKjusZFmtTF1Kq4httmlCwjBy2DgBN0OsgTSZ_oInzsJ3hNQ; oauth2_referer=etax.jiangsu.chinatax.gov.cn; znhd-ssotoken=1d8946f3d5dc4c719b00f36dc315274c; ZNHD_SECURITY_CHECK_TOKEN=a3e8f029c835438c8bb83dd149d2c963; tpass_k238ck9eedkb48a9a5d7k5c2c5kkka58=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijk2OTIxNDlhYTM0NTRmYTU4MjNjYTI3YTY5N2M4OWI5In0.R5gvVfS7TSDAjv8Xesi7Mi1svyE8dK-jDNiDCledU-umtW_facMuiKL1PL0sb3CNXPoXL-A6ozlfz0zZZmbHtQ"
    let Cookie = "tpass_k238ck9eedkb48a9a5d7k5c2c5kkka58=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjhhOWExNTk2OWVkZjRmZDFhMTAwMjlhMjdkMGI2OWUwIn0.D-eX5c7YcNY8WW2VqLMSI5RJboIiHhxoy2lQub8SbninvtCmkgH-JwTUamMmDwMlfs6tjTEo691h5pRy5h5K_w"
    const Headers ={
        "Cookie":Cookie,
    }

    try {
        const responseBody = await axios.post(url,requestBody,{headers:Headers})
        if (responseBody.data.code) {
            console.log("异常原因为：" + responseBody.data.msg + "定时器取消");
            clearInterval(intervalId); // 取消定时器
        }else{
            console.log("返回结果:",responseBody.data.Response.Data)
        }
    }catch (error){
        console.log("请求出错"+error)
    }

}

/*
* 缴款信息查询
* */
async function JKXXCX(){
    /*
    *加密
    * */
    const requestBody = {
        "jkrqq":"",
        "jkrqz":"",
        "skssqq":"",
        "skssqz":"",
        "skzlDm":"",
        "zspmDm":"",
        "zsxmDm":"",
        "pageNum":1,
        "pageSize":10
    }

    /*
    * 执行参数
    * */
    const url = `https://etax.jiangsu.chinatax.gov.cn:8443/szc/szzh/sjswszzh/zhcx/jkxx/v1/DescribeJkxxcx`
    let Cookie = "tpass_k238ck9eedkb48a9a5d7k5c2c5kkka58=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijk2OTIxNDlhYTM0NTRmYTU4MjNjYTI3YTY5N2M4OWI5In0.R5gvVfS7TSDAjv8Xesi7Mi1svyE8dK-jDNiDCledU-umtW_facMuiKL1PL0sb3CNXPoXL-A6ozlfz0zZZmbHtQ"
        // "sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%2219273fee980582-0dcbe78f10a5308-26001b51-2073600-19273fee981b2c%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTkyNzNmZWU5ODA1ODItMGRjYmU3OGYxMGE1MzA4LTI2MDAxYjUxLTIwNzM2MDAtMTkyNzNmZWU5ODFiMmMifQ%3D%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A%22%22%7D%2C%22%24device_id%22%3A%2219273fee980582-0dcbe78f10a5308-26001b51-2073600-19273fee981b2c%22%7D; Hm_lvt_cdef0adc5294fae9be3436c5a50f805f=1736245772; SF_cookie_43=16290870; oauth2_referer=etax.jiangsu.chinatax.gov.cn; tpass_tct8zta97w6c46zdt9zc2648227df5z2=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImZjNzU1NWQwYWVjZDRhMmY4ODM4MzBkYzM5MzE1OTZmIn0.fun5uNzZOkOnQv1Rcxe58zaGPljpa1e5ITh66iBjKLaoSEo2fhr07cxcmBMR2oaXsJpkworwiqwuNdFKgtGQ7w; refer=etax.zhejiang.chinatax.gov.cn; dzfp-ssotoken=45dc467d27f442e5b4cd801cef695e62; SSO_SECURITY_CHECK_TOKEN=18bce01ed0ff44a28c541a5982975bb0; SSOSESSIONID=mbreChAcRK6Q1XVFP_28k9Yaas49T7Fvnb1YDTl36ihHwjmw-8xd!-16901149; tpass_mcsc7e2ssscb4sfmbsmas35sass2753b=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImI5N2NhNTFiMmFkODRlNGFiZjBiYmJmZWJmNzRhZWU0In0.cwTLnM2Iu3l2RxayfWrpMHdzr5uegJSSFrrcApjKjusZFmtTF1Kq4httmlCwjBy2DgBN0OsgTSZ_oInzsJ3hNQ; znhd-ssotoken=1d8946f3d5dc4c719b00f36dc315274c; ZNHD_SECURITY_CHECK_TOKEN=a3e8f029c835438c8bb83dd149d2c963; tpass_k238ck9eedkb48a9a5d7k5c2c5kkka58=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijk2OTIxNDlhYTM0NTRmYTU4MjNjYTI3YTY5N2M4OWI5In0.R5gvVfS7TSDAjv8Xesi7Mi1svyE8dK-jDNiDCledU-umtW_facMuiKL1PL0sb3CNXPoXL-A6ozlfz0zZZmbHtQ; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%2219273fee980582-0dcbe78f10a5308-26001b51-2073600-19273fee981b2c%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTkyNzNmZWU5ODA1ODItMGRjYmU3OGYxMGE1MzA4LTI2MDAxYjUxLTIwNzM2MDAtMTkyNzNmZWU5ODFiMmMifQ%3D%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A%22%22%7D%2C%22%24device_id%22%3A%2219273fee980582-0dcbe78f10a5308-26001b51-2073600-19273fee981b2c%22%7D; Hm_lvt_cdef0adc5294fae9be3436c5a50f805f=1736245772; SF_cookie_43=16290870; tpass_tct8zta97w6c46zdt9zc2648227df5z2=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImZjNzU1NWQwYWVjZDRhMmY4ODM4MzBkYzM5MzE1OTZmIn0.fun5uNzZOkOnQv1Rcxe58zaGPljpa1e5ITh66iBjKLaoSEo2fhr07cxcmBMR2oaXsJpkworwiqwuNdFKgtGQ7w; refer=etax.zhejiang.chinatax.gov.cn; SSOSESSIONID=mbreChAcRK6Q1XVFP_28k9Yaas49T7Fvnb1YDTl36ihHwjmw-8xd!-16901149; tpass_mcsc7e2ssscb4sfmbsmas35sass2753b=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImI5N2NhNTFiMmFkODRlNGFiZjBiYmJmZWJmNzRhZWU0In0.cwTLnM2Iu3l2RxayfWrpMHdzr5uegJSSFrrcApjKjusZFmtTF1Kq4httmlCwjBy2DgBN0OsgTSZ_oInzsJ3hNQ; oauth2_referer=etax.jiangsu.chinatax.gov.cn; znhd-ssotoken=1d8946f3d5dc4c719b00f36dc315274c; ZNHD_SECURITY_CHECK_TOKEN=a3e8f029c835438c8bb83dd149d2c963; tpass_k238ck9eedkb48a9a5d7k5c2c5kkka58=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijk2OTIxNDlhYTM0NTRmYTU4MjNjYTI3YTY5N2M4OWI5In0.R5gvVfS7TSDAjv8Xesi7Mi1svyE8dK-jDNiDCledU-umtW_facMuiKL1PL0sb3CNXPoXL-A6ozlfz0zZZmbHtQ"
    const Headers ={
        "Cookie":Cookie,
    }

    try {
        const responseBody = await axios.post(url,requestBody,{headers:Headers})
        if (responseBody.data.code) {
            console.log("异常原因为：" + responseBody.data.msg + "定时器取消");
            clearInterval(intervalId); // 取消定时器
        }else{
            console.log("返回结果:",responseBody.data.Response.Data)
        }
    }catch (error){
        console.log("请求出错"+error)
    }

}

/*
* 纳税人信息查询
* */
async function NSRXXCX(){
    /*
    *加密
    * */
    const requestBody = {}

    /*
    * 执行参数
    * */
    const url = `https://etax.jiangsu.chinatax.gov.cn:8443/szc/szzh/sjswszzh/zhcx/nsrxx/v1/DescribeNsrxxJcxx?djxh=`
    let Cookie = "tpass_k238ck9eedkb48a9a5d7k5c2c5kkka58=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijk2OTIxNDlhYTM0NTRmYTU4MjNjYTI3YTY5N2M4OWI5In0.R5gvVfS7TSDAjv8Xesi7Mi1svyE8dK-jDNiDCledU-umtW_facMuiKL1PL0sb3CNXPoXL-A6ozlfz0zZZmbHtQ"
        // "sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%2219273fee980582-0dcbe78f10a5308-26001b51-2073600-19273fee981b2c%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTkyNzNmZWU5ODA1ODItMGRjYmU3OGYxMGE1MzA4LTI2MDAxYjUxLTIwNzM2MDAtMTkyNzNmZWU5ODFiMmMifQ%3D%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A%22%22%7D%2C%22%24device_id%22%3A%2219273fee980582-0dcbe78f10a5308-26001b51-2073600-19273fee981b2c%22%7D; Hm_lvt_cdef0adc5294fae9be3436c5a50f805f=1736245772; SF_cookie_43=16290870; oauth2_referer=etax.jiangsu.chinatax.gov.cn; tpass_tct8zta97w6c46zdt9zc2648227df5z2=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImZjNzU1NWQwYWVjZDRhMmY4ODM4MzBkYzM5MzE1OTZmIn0.fun5uNzZOkOnQv1Rcxe58zaGPljpa1e5ITh66iBjKLaoSEo2fhr07cxcmBMR2oaXsJpkworwiqwuNdFKgtGQ7w; refer=etax.zhejiang.chinatax.gov.cn; dzfp-ssotoken=45dc467d27f442e5b4cd801cef695e62; SSO_SECURITY_CHECK_TOKEN=18bce01ed0ff44a28c541a5982975bb0; SSOSESSIONID=mbreChAcRK6Q1XVFP_28k9Yaas49T7Fvnb1YDTl36ihHwjmw-8xd!-16901149; tpass_mcsc7e2ssscb4sfmbsmas35sass2753b=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImI5N2NhNTFiMmFkODRlNGFiZjBiYmJmZWJmNzRhZWU0In0.cwTLnM2Iu3l2RxayfWrpMHdzr5uegJSSFrrcApjKjusZFmtTF1Kq4httmlCwjBy2DgBN0OsgTSZ_oInzsJ3hNQ; znhd-ssotoken=1d8946f3d5dc4c719b00f36dc315274c; ZNHD_SECURITY_CHECK_TOKEN=a3e8f029c835438c8bb83dd149d2c963; tpass_k238ck9eedkb48a9a5d7k5c2c5kkka58=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijk2OTIxNDlhYTM0NTRmYTU4MjNjYTI3YTY5N2M4OWI5In0.R5gvVfS7TSDAjv8Xesi7Mi1svyE8dK-jDNiDCledU-umtW_facMuiKL1PL0sb3CNXPoXL-A6ozlfz0zZZmbHtQ; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%2219273fee980582-0dcbe78f10a5308-26001b51-2073600-19273fee981b2c%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTkyNzNmZWU5ODA1ODItMGRjYmU3OGYxMGE1MzA4LTI2MDAxYjUxLTIwNzM2MDAtMTkyNzNmZWU5ODFiMmMifQ%3D%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A%22%22%7D%2C%22%24device_id%22%3A%2219273fee980582-0dcbe78f10a5308-26001b51-2073600-19273fee981b2c%22%7D; Hm_lvt_cdef0adc5294fae9be3436c5a50f805f=1736245772; SF_cookie_43=16290870; tpass_tct8zta97w6c46zdt9zc2648227df5z2=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImZjNzU1NWQwYWVjZDRhMmY4ODM4MzBkYzM5MzE1OTZmIn0.fun5uNzZOkOnQv1Rcxe58zaGPljpa1e5ITh66iBjKLaoSEo2fhr07cxcmBMR2oaXsJpkworwiqwuNdFKgtGQ7w; refer=etax.zhejiang.chinatax.gov.cn; SSOSESSIONID=mbreChAcRK6Q1XVFP_28k9Yaas49T7Fvnb1YDTl36ihHwjmw-8xd!-16901149; tpass_mcsc7e2ssscb4sfmbsmas35sass2753b=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImI5N2NhNTFiMmFkODRlNGFiZjBiYmJmZWJmNzRhZWU0In0.cwTLnM2Iu3l2RxayfWrpMHdzr5uegJSSFrrcApjKjusZFmtTF1Kq4httmlCwjBy2DgBN0OsgTSZ_oInzsJ3hNQ; oauth2_referer=etax.jiangsu.chinatax.gov.cn; znhd-ssotoken=1d8946f3d5dc4c719b00f36dc315274c; ZNHD_SECURITY_CHECK_TOKEN=a3e8f029c835438c8bb83dd149d2c963; tpass_k238ck9eedkb48a9a5d7k5c2c5kkka58=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijk2OTIxNDlhYTM0NTRmYTU4MjNjYTI3YTY5N2M4OWI5In0.R5gvVfS7TSDAjv8Xesi7Mi1svyE8dK-jDNiDCledU-umtW_facMuiKL1PL0sb3CNXPoXL-A6ozlfz0zZZmbHtQ"
    const Headers ={
        "Cookie":Cookie,
    }

    try {
        const responseBody = await axios.post(url,requestBody,{headers:Headers})
        if (responseBody.data.code) {
            console.log("异常原因为：" + responseBody.data.msg + "定时器取消");
            clearInterval(intervalId); // 取消定时器
        }else{
            console.log("返回结果:",responseBody.data.Response.Data)
        }
    }catch (error){
        console.log("请求出错"+error)
    }

}

/*
* 资格信息查询
* */
async function ZGXXCX(){

    const requestBody = {}

    /*
    * 执行参数
    * */
    const url = `https://etax.jiangsu.chinatax.gov.cn:8443/szc/szzh/sjswszzh/zhcx/nsrxx/v1/DescribeNsrxxJcxx?djxh=`
    let Cookie = "tpass_k238ck9eedkb48a9a5d7k5c2c5kkka58=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjdmZThmZDNkMDYzNzQ0YzViZDQ3Yjk2MWJmMzdhYjk4In0.l_yxNnE7mhiFYATgTPTDMKrVsQkL2dPpZv8eLVZKFQi4V5thbyIVihHbgE3jKyS0lb9WcV4HXFo1TJFfQpSumA"
    // "sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%2219273fee980582-0dcbe78f10a5308-26001b51-2073600-19273fee981b2c%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTkyNzNmZWU5ODA1ODItMGRjYmU3OGYxMGE1MzA4LTI2MDAxYjUxLTIwNzM2MDAtMTkyNzNmZWU5ODFiMmMifQ%3D%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A%22%22%7D%2C%22%24device_id%22%3A%2219273fee980582-0dcbe78f10a5308-26001b51-2073600-19273fee981b2c%22%7D; Hm_lvt_cdef0adc5294fae9be3436c5a50f805f=1736245772; SF_cookie_43=16290870; oauth2_referer=etax.jiangsu.chinatax.gov.cn; tpass_tct8zta97w6c46zdt9zc2648227df5z2=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImZjNzU1NWQwYWVjZDRhMmY4ODM4MzBkYzM5MzE1OTZmIn0.fun5uNzZOkOnQv1Rcxe58zaGPljpa1e5ITh66iBjKLaoSEo2fhr07cxcmBMR2oaXsJpkworwiqwuNdFKgtGQ7w; refer=etax.zhejiang.chinatax.gov.cn; dzfp-ssotoken=45dc467d27f442e5b4cd801cef695e62; SSO_SECURITY_CHECK_TOKEN=18bce01ed0ff44a28c541a5982975bb0; SSOSESSIONID=mbreChAcRK6Q1XVFP_28k9Yaas49T7Fvnb1YDTl36ihHwjmw-8xd!-16901149; tpass_mcsc7e2ssscb4sfmbsmas35sass2753b=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImI5N2NhNTFiMmFkODRlNGFiZjBiYmJmZWJmNzRhZWU0In0.cwTLnM2Iu3l2RxayfWrpMHdzr5uegJSSFrrcApjKjusZFmtTF1Kq4httmlCwjBy2DgBN0OsgTSZ_oInzsJ3hNQ; znhd-ssotoken=1d8946f3d5dc4c719b00f36dc315274c; ZNHD_SECURITY_CHECK_TOKEN=a3e8f029c835438c8bb83dd149d2c963; tpass_k238ck9eedkb48a9a5d7k5c2c5kkka58=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijk2OTIxNDlhYTM0NTRmYTU4MjNjYTI3YTY5N2M4OWI5In0.R5gvVfS7TSDAjv8Xesi7Mi1svyE8dK-jDNiDCledU-umtW_facMuiKL1PL0sb3CNXPoXL-A6ozlfz0zZZmbHtQ; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%2219273fee980582-0dcbe78f10a5308-26001b51-2073600-19273fee981b2c%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTkyNzNmZWU5ODA1ODItMGRjYmU3OGYxMGE1MzA4LTI2MDAxYjUxLTIwNzM2MDAtMTkyNzNmZWU5ODFiMmMifQ%3D%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A%22%22%7D%2C%22%24device_id%22%3A%2219273fee980582-0dcbe78f10a5308-26001b51-2073600-19273fee981b2c%22%7D; Hm_lvt_cdef0adc5294fae9be3436c5a50f805f=1736245772; SF_cookie_43=16290870; tpass_tct8zta97w6c46zdt9zc2648227df5z2=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImZjNzU1NWQwYWVjZDRhMmY4ODM4MzBkYzM5MzE1OTZmIn0.fun5uNzZOkOnQv1Rcxe58zaGPljpa1e5ITh66iBjKLaoSEo2fhr07cxcmBMR2oaXsJpkworwiqwuNdFKgtGQ7w; refer=etax.zhejiang.chinatax.gov.cn; SSOSESSIONID=mbreChAcRK6Q1XVFP_28k9Yaas49T7Fvnb1YDTl36ihHwjmw-8xd!-16901149; tpass_mcsc7e2ssscb4sfmbsmas35sass2753b=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImI5N2NhNTFiMmFkODRlNGFiZjBiYmJmZWJmNzRhZWU0In0.cwTLnM2Iu3l2RxayfWrpMHdzr5uegJSSFrrcApjKjusZFmtTF1Kq4httmlCwjBy2DgBN0OsgTSZ_oInzsJ3hNQ; oauth2_referer=etax.jiangsu.chinatax.gov.cn; znhd-ssotoken=1d8946f3d5dc4c719b00f36dc315274c; ZNHD_SECURITY_CHECK_TOKEN=a3e8f029c835438c8bb83dd149d2c963; tpass_k238ck9eedkb48a9a5d7k5c2c5kkka58=eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijk2OTIxNDlhYTM0NTRmYTU4MjNjYTI3YTY5N2M4OWI5In0.R5gvVfS7TSDAjv8Xesi7Mi1svyE8dK-jDNiDCledU-umtW_facMuiKL1PL0sb3CNXPoXL-A6ozlfz0zZZmbHtQ"
    const Headers ={
        "Cookie":Cookie,
    }

    try {
        const responseBody = await axios.post(url,requestBody,{headers:Headers})
        if (responseBody.data.code) {
            console.log("异常原因为：" + responseBody.data.msg + "定时器取消");
            clearInterval(intervalId); // 取消定时器
        }else{
            console.log("返回结果:",responseBody.data.Response.Data)
        }
    }catch (error){
        console.log("请求出错"+error)
    }

}
